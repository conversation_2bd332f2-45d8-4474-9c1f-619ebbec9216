/****************************************************************************************
 * 文 件 名 : dpi_http.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy          2018/07/06
编码: wangy            2018/07/06
修改: xuxn          2019/03/11
修改: chunli         2020/07/10  输出HTTP BODY解析错误的报文
修改: chunli         2020/07/13  采用新版TCP重组
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <string.h>
#include <sys/time.h>
#include <openssl/aes.h>

#include "dpi_common.h"

#include "dpi_conversation.h"
#include "dpi_flow.h"
#include "list.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_ocsp.h"
#include "post.h"
#include "dpi_high_app_protos.h"
#include "dpi_utils.h"
#include "dpi_http.h"
#include <yaProtoRecord/precord_schema.h>
#include "dpi_pschema.h"
#include "openssl/md5.h"

#define HTTP_VALUE_DUP(flow, from, len, to, size)       \
do                                                      \
{                                                       \
    if(to || len==0)                                    \
    {   if(len == 0) {                                  \
            /* http中只有key没有value   */              \
            (to) = (typeof(to))strdup(" ");             \
            (size) = strlen((const char*)(to));         \
        }                                               \
        break;                                          \
    }                                                   \
    (to) = (typeof(to))alloc_memdup((flow->memAc), (from), (len));    \
    (size) =(typeof(size))(len);                                     \
} while(0);                                             \

uint64_t http_new_flow = 0;
uint64_t http_free_flow = 0;

extern struct rte_mempool *tbl_log_content_mempool_256k;
extern rte_atomic64_t log_256k_fail;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
size_t ws_base64_decode_inplace(char *s);
void uri_decode(char *src, char *dst, int src_len, int dst_len) ;
static dpi_field_table  http_field_array[] = {
    DPI_FIELD_D(EM_HTTP_METHOD,                      YA_FT_STRING,               "Method"),
    DPI_FIELD_D(EM_HTTP_URI,                         YA_FT_STRING,               "URI"),
    DPI_FIELD_D(EM_HTTP_URI_COUNT,                   YA_FT_UINT32,               "URI-Count"),
    DPI_FIELD_D(EM_HTTP_URI_KEYS,                    YA_FT_STRING,               "URI-Keys"),
    DPI_FIELD_D(EM_HTTP_URI_KEYS_COUNT,              YA_FT_UINT32,               "URI-Keys-Count"),
    DPI_FIELD_D(EM_HTTP_URI_PATH,                    YA_FT_STRING,               "URI-Path"),
    DPI_FIELD_D(EM_HTTP_URI_PATH_COUNT,              YA_FT_UINT32,               "URI-Path-Count"),
    DPI_FIELD_D(EM_HTTP_VERSION,                     YA_FT_STRING,               "Version"),
    DPI_FIELD_D(EM_HTTP_STATUS,                      YA_FT_STRING,               "Status"),
    DPI_FIELD_D(EM_HTTP_RESPONSESTATUS,              YA_FT_STRING,               "ResponseStatus"),
    DPI_FIELD_D(EM_HTTP_CACHE_CONTROL,               YA_FT_STRING,               "Cache-Control"),
    DPI_FIELD_D(EM_HTTP_CONNECTION,                  YA_FT_STRING,               "Connection"),
    DPI_FIELD_D(EM_HTTP_COOKIE,                      YA_FT_STRING,               "Cookie"),
    DPI_FIELD_D(EM_HTTP_COOKIE2,                     YA_FT_STRING,               "Cookie2"),
    DPI_FIELD_D(EM_HTTP_COOKIE_KEYS,                 YA_FT_STRING,               "CookieKeys"),
    DPI_FIELD_D(EM_HTTP_DATE,                        YA_FT_STRING,               "Date"),
    DPI_FIELD_D(EM_HTTP_PRAGMA,                      YA_FT_STRING,               "Pragma"),
    DPI_FIELD_D(EM_HTTP_TRAILER,                     YA_FT_STRING,               "Trailer"),
    DPI_FIELD_D(EM_HTTP_TRANSFER_ENCODING,           YA_FT_STRING,               "Transfer-Encoding"),
    DPI_FIELD_D(EM_HTTP_UPGRADE,                     YA_FT_STRING,               "Upgrade"),
    DPI_FIELD_D(EM_HTTP_VIA,                         YA_FT_STRING,               "Via"),
    DPI_FIELD_D(EM_HTTP_VIA_COUNT,                   YA_FT_UINT32,               "Via-Count"),
    DPI_FIELD_D(EM_HTTP_WARNING,                     YA_FT_STRING,               "Warning"),
    DPI_FIELD_D(EM_HTTP_ACCEPT,                      YA_FT_STRING,               "Accept"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_CHARSET,              YA_FT_STRING,               "Accept-Charset"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_ENCODING,             YA_FT_STRING,               "Accept-Encoding"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_LANGUAGE,             YA_FT_STRING,               "Accept-Language"),
    DPI_FIELD_D(EM_HTTP_AUTHORIZATION,               YA_FT_STRING,               "Authorization"),
    DPI_FIELD_D(EM_HTTP_AUTH_USERNAME,               YA_FT_STRING,               "Username"),
    DPI_FIELD_D(EM_HTTP_EXPECT,                      YA_FT_STRING,               "Expect"),
    DPI_FIELD_D(EM_HTTP_FROM,                        YA_FT_STRING,               "From"),
    DPI_FIELD_D(EM_HTTP_HOST,                        YA_FT_STRING,               "Host"),
    DPI_FIELD_D(EM_HTTP_HOST_COUNT,                  YA_FT_UINT32,               "Host-Count"),
    DPI_FIELD_D(EM_HTTP_IF_MATCH,                    YA_FT_STRING,               "If-Match"),
    DPI_FIELD_D(EM_HTTP_IF_MODIFIED_SINCE,           YA_FT_STRING,               "If-Modified-Since"),
    DPI_FIELD_D(EM_HTTP_IF_NONE_MATCH,               YA_FT_STRING,               "If-None-Match"),
    DPI_FIELD_D(EM_HTTP_IF_RANGE,                    YA_FT_STRING,               "If-Range"),
    DPI_FIELD_D(EM_HTTP_IF_UNMODIFIED_SINCE,         YA_FT_STRING,               "If-Unmodified-Since"),
    DPI_FIELD_D(EM_HTTP_MAX_FORWARDS,                YA_FT_UINT32,               "Max-Forwards"),
    DPI_FIELD_D(EM_HTTP_PROXY_AUTHORIZATION,         YA_FT_STRING,               "Proxy-Authorization"),
    DPI_FIELD_D(EM_HTTP_PROXY_TYPE,                  YA_FT_STRING,               "Proxy-Type"),
    DPI_FIELD_D(EM_HTTP_PROXY_LOGIN,                 YA_FT_STRING,               "Proxy-Login"),
    DPI_FIELD_D(EM_HTTP_PROXY_AUTHORIZATION_INFO,    YA_FT_STRING,               "Proxy-Authorization-Info"),
    DPI_FIELD_D(EM_HTTP_RANGE,                       YA_FT_STRING,               "Range"),
    DPI_FIELD_D(EM_HTTP_REFERER,                     YA_FT_STRING,               "Referer"),
    DPI_FIELD_D(EM_HTTP_TE,                          YA_FT_STRING,               "TE"),
    DPI_FIELD_D(EM_HTTP_USER_AGENT,                  YA_FT_STRING,               "User-Agent"),
    DPI_FIELD_D(EM_HTTP_USER_AGENT_NUM,              YA_FT_UINT32,               "UserAgentCount"),
    DPI_FIELD_D(EM_HTTP_PROXY_PROXYAGENT,            YA_FT_STRING,               "Proxy-Agent"),
    DPI_FIELD_D(EM_HTTP_USER_CPU,                    YA_FT_STRING,               "UA-CPU"),
    DPI_FIELD_D(EM_HTTP_USER_OS,                     YA_FT_STRING,               "UA-OS"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_RANGES,               YA_FT_STRING,               "Accept-Ranges"),
    DPI_FIELD_D(EM_HTTP_AGE,                         YA_FT_STRING,               "Age"),
    DPI_FIELD_D(EM_HTTP_ETAG,                        YA_FT_STRING,               "ETag"),
    DPI_FIELD_D(EM_HTTP_LOCATION,                    YA_FT_STRING,               "Location"),
    DPI_FIELD_D(EM_HTTP_PROXY_AUTHENTICATE,          YA_FT_STRING,               "Proxy-Authenticate"),
    DPI_FIELD_D(EM_HTTP_INQUIRY_TYPE,                YA_FT_STRING,               "Inquiry_Type"),
    DPI_FIELD_D(EM_HTTP_PROXY_CONNECT_PORT,          YA_FT_STRING,               "Proxy-Connect_Port"),
    DPI_FIELD_D(EM_HTTP_PROXY_CONNECT_HOST,          YA_FT_STRING,               "Proxy-Connect_Host"),
    DPI_FIELD_D(EM_HTTP_RETRY_AFTER,                 YA_FT_STRING,               "Retry-After"),
    DPI_FIELD_D(EM_HTTP_SERVER,                      YA_FT_STRING,               "Server"),
    DPI_FIELD_D(EM_HTTP_VARY,                        YA_FT_STRING,               "Vary"),
    DPI_FIELD_D(EM_HTTP_WWW_AUTHENTICATE,            YA_FT_STRING,               "WWW-Authenticate"),
    DPI_FIELD_D(EM_HTTP_ALLOW,                       YA_FT_STRING,               "Allow"),
    DPI_FIELD_D(EM_HTTP_CONTENT_ENCODING_S,          YA_FT_UINT32,               "Content-Encoding-s"),
    DPI_FIELD_D(EM_HTTP_CONTENT_ENCODING_C,          YA_FT_STRING,               "Content-Encoding-c"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LANGUAGE,            YA_FT_STRING,               "Content-Language"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LENGTH,              YA_FT_UINT32,               "Content-Length"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LENGTH_REQ,          YA_FT_UINT32,               "reqContent-Length"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LENGTH_RSP,          YA_FT_UINT32,               "rspContent-Length"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LOCATION,            YA_FT_STRING,               "Content-Location"),
    DPI_FIELD_D(EM_HTTP_CONTENT_MD5,                 YA_FT_STRING,               "Content-MD5"),
    DPI_FIELD_D(EM_HTTP_CONTENT_RANGE,               YA_FT_STRING,               "Content-Range"),
    DPI_FIELD_D(EM_HTTP_CONTENT_TYPE,                YA_FT_STRING,               "Content-Type"),
    DPI_FIELD_D(EM_HTTP_EXPIRES,                     YA_FT_STRING,               "Expires"),
    DPI_FIELD_D(EM_HTTP_REFRESH,                     YA_FT_STRING,               "Refresh"),
    DPI_FIELD_D(EM_HTTP_LAST_MODIFIED,               YA_FT_STRING,               "Last-Modified"),
    DPI_FIELD_D(EM_HTTP_X_FORWARDED_FOR,             YA_FT_STRING,               "X-Forwarded-For"),
    DPI_FIELD_D(EM_HTTP_SET_COOKIE,                  YA_FT_STRING,               "Set-Cookie"),
    DPI_FIELD_D(EM_HTTP_SET_COOKIE2,                 YA_FT_STRING,               "Set-Cookie2"),
    DPI_FIELD_D(EM_HTTP_DNT,                         YA_FT_STRING,               "DNT"),
    DPI_FIELD_D(EM_HTTP_X_POWERED_BY,                YA_FT_STRING,               "X-Powered-By"),
    DPI_FIELD_D(EM_HTTP_P3P,                         YA_FT_STRING,               "P3P"),
    DPI_FIELD_D(EM_HTTP_CLIENT_HEAD,                 YA_FT_STRING,               "Client_Head"),
    DPI_FIELD_D(EM_HTTP_CLIENT_BODY,                 YA_FT_STRING,               "Client_Body"),
    DPI_FIELD_D(EM_HTTP_GET_SVR_REQ_URL,             YA_FT_STRING,               "Get_Svr_Req_Url"),
    DPI_FIELD_D(EM_HTTP_ENTITY_TITLE_HEAD,           YA_FT_STRING,               "Entity_Title_Head"),
    DPI_FIELD_D(EM_HTTP_H_A_NUMBER,                  YA_FT_STRING,               "H_A_NUMBER"),
    DPI_FIELD_D(EM_HTTP_H_X_NUMBER,                  YA_FT_STRING,               "H_X_NUMBER"),
    DPI_FIELD_D(EM_HTTP_APPTYPE,                     YA_FT_STRING,               "App_Type"),
    DPI_FIELD_D(EM_HTTP_X_HTTP_TOKEN,                YA_FT_STRING,               "X_http_token"),
    DPI_FIELD_D(EM_HTTP_X_COOKIE_TOKEN,              YA_FT_STRING,               "X_cookie_token"),
    DPI_FIELD_D(EM_HTTP_X_SINKHOLE,                  YA_FT_STRING,               "X_Sinkhole"),
    DPI_FIELD_D(EM_HTTP_REQ_HEAD_FIELDS,             YA_FT_STRING,               "reqHeadFields"),
    DPI_FIELD_D(EM_HTTP_REQ_HEAD_FIELDS_MD5,         YA_FT_STRING,               "reqHeadFieldsMD5"),
    DPI_FIELD_D(EM_HTTP_REQ_HEAD_FIELDS_NUM,         YA_FT_UINT32,               "reqHeadFieldsCount"),
    DPI_FIELD_D(EM_HTTP_REQ_METHOD_NUM,              YA_FT_UINT32,               "reqMethodCount"),
    DPI_FIELD_D(EM_HTTP_REQ_VERSION_NUM,             YA_FT_UINT32,               "reqVersionCount"),
    DPI_FIELD_D(EM_HTTP_REQ_BODY,                    YA_FT_STRING,               "reqBody"),
    DPI_FIELD_D(EM_HTTP_RSP_HEAD_FIELDS,             YA_FT_STRING,               "rspHeadFields"),
    DPI_FIELD_D(EM_HTTP_RSP_HEAD_FIELDS_MD5,         YA_FT_STRING,               "rspHeadFieldsMD5"),
    DPI_FIELD_D(EM_HTTP_RSP_CACHE_CONTROL,           YA_FT_STRING,               "rspCache-Control"),
    DPI_FIELD_D(EM_HTTP_RSP_CONNECTION,              YA_FT_STRING,               "rspConnection"),
    DPI_FIELD_D(EM_HTTP_RSP_PRAGMA,                  YA_FT_STRING,               "rspPragma"),
    DPI_FIELD_D(EM_HTTP_RSP_ACCEPT_RANGES,           YA_FT_STRING,               "rspAccept-Ranges"),
    DPI_FIELD_D(EM_HTTP_RSP_ACCEPT_CHARSET,          YA_FT_STRING,               "rspAccept-Charset"),
    DPI_FIELD_D(EM_HTTP_RSP_CONTENT_TYPE,            YA_FT_STRING,               "rspContent-Type"),
    DPI_FIELD_D(EM_HTTP_RSP_VERSION,                 YA_FT_STRING,               "rspVersion"),
    DPI_FIELD_D(EM_HTTP_RSP_BODY,                    YA_FT_STRING,               "rspBody"),
    DPI_FIELD_D(EM_HTTP_RSP_FULLTEXT_LEN,            YA_FT_UINT32,               "rspFullTextLen"),
    DPI_FIELD_D(EM_HTTP_URL,                         YA_FT_STRING,               "Url"),
    DPI_FIELD_D(EM_HTTP_CONDISPUP,                   YA_FT_STRING,               "conDispUp"),
    DPI_FIELD_D(EM_HTTP_CONDISPDOWN,                 YA_FT_STRING,               "conDispDown"),
    DPI_FIELD_D(EM_HTTP_URISEARCH,                   YA_FT_STRING,               "uriSearch"),
    DPI_FIELD_D(EM_HTTP_IMSI,                        YA_FT_STRING,               "imsi"),
    DPI_FIELD_D(EM_HTTP_IMEI,                        YA_FT_STRING,               "imei"),
#ifdef DPI_SDT_YNAO 
    DPI_FIELD_D(EM_HTTP_LOCAL_FILENAME,              YA_FT_STRING,               "localFilename"),
    DPI_FIELD_D(EM_HTTP_LOCAL_FILEPATH,              YA_FT_STRING,               "localFilepath"),
    DPI_FIELD_D( EM_HTTP_SET_COOKIE_VALUE,              YA_FT_STRING,           "setCookieValue"),
#endif
#ifdef DPI_SDT_ZDY
    DPI_FIELD_D(EM_HTTP_V51,                         YA_FT_STRING,               "V51"),
    DPI_FIELD_D(EM_HTTP_LOCAL_FILENAME,              YA_FT_STRING,               "localFilename"),
    DPI_FIELD_D(EM_HTTP_CONDISP,                     YA_FT_STRING,               "conDisp"),
    DPI_FIELD_D(EM_HTTP_REMAINDERLINE,               YA_FT_STRING,               "remainderLine"),
#endif
};




static struct {const char *method; int len;} HTTP_METHOD[] =
{
    {"GET ",     4},
    {"POST ",    5},
    {"OPTIONS ", 8},
    {"HEAD ",    5},
    {"PUT ",     4},
    {"DELETE ",  7},
    {"CONNECT ", 8},
    {"PROPFIND ",9},
    {"REPORT ",  7},
    {"PATCH",    6},
    {NULL,       0}
};

static struct {
  const char *c_type;
  int         len;
} HTTP_CTYPE[] = {{"image/", 6}, {"audio/", 6}, {"video/", 6}, {"font/", 5}, {"text/", 5}, {"application/", 12}, {NULL, 0}};

static const char* searchHost[] = {
  "bing",
  "baidu",
  "google",
  "sougou",
  "jinan",
  "yichuan",
  NULL
};
static const char* searchKey[] = {
  "wb",
  "q",
  "query",
  "word",
  NULL
};

typedef struct{
    struct http_session * session;
    struct http_info *http_value;
}USER;
static inline void _free_header(gpointer data)
{
    free(data);
}

static inline void _free_key_value(gpointer data)
{
    struct header_tmp_value *_value = (struct header_tmp_value *)data;

    if (_value->need_free)
        dpi_free((void *)_value->ptr);
    dpi_free(data);
}

static void _find_hash_write_log_delete(struct http_info *http_value, const char *header_name, precord_t *record, int *idx)
{
    gpointer _value;
    struct header_value *value;

    if(http_value->table==NULL){
         write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
         return ;
    }

    _value = g_hash_table_lookup(http_value->table, header_name);
    value = (struct header_value *)_value;
    if (!value)
        write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
    else
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)value->ptr, value->len);
    g_hash_table_remove(http_value->table, header_name);
}

/* 0-没命中规则，1-命中规则，是想要的数据写tbl */
static int _http_content_type_filter(char *buf)
{
    if(g_config.http.num<=0){
        return 1;
    }
    regmatch_t pmatch[1];
    const size_t nmatch = 1;
    int status=0;
    int i=0;
    for(i=0;i<g_config.http.num;i++){
        status=regexec(&g_config.http.reg[i],buf,nmatch,pmatch,0);
        if(0==status){
            return 1;
        }
    }

    return 0;
}

static int _recreate_http_tbl_dir(int thread_id)
{
    struct timeval tv;
    gettimeofday(&tv, NULL);

    bzero(flow_thread_info[thread_id].file_tbl_dir,COMMON_FILE_PATH);
    bzero(flow_thread_info[thread_id].file_tbl_name,COMMON_FILE_PATH);
    flow_thread_info[thread_id].file_count=0;
    flow_thread_info[thread_id].timeout_sec=g_config.g_now_time;

    if(g_config.show_task_id){
        snprintf(flow_thread_info[thread_id].file_tbl_dir,COMMON_FILE_PATH,
                                  "%s/%s/%s_%06ld_http_n_%03d_%s.writing",
                                  g_config.tbl_out_dir,
                                  tbl_log_array[TBL_LOG_HTTP].protoname,
                                  time_to_datetime(g_config.g_now_time),
                                  tv.tv_usec,thread_id,
                                  g_config.task_id);
    }
    else{
        snprintf(flow_thread_info[thread_id].file_tbl_dir,COMMON_FILE_PATH,
                                  "%s/%s/%s_%06ld_http_n_%03d.writing",
                                  g_config.tbl_out_dir,
                                  tbl_log_array[TBL_LOG_HTTP].protoname,
                                  time_to_datetime(g_config.g_now_time),
                                  tv.tv_usec,thread_id);
    }

    mkdirs(flow_thread_info[thread_id].file_tbl_dir);

    if(g_config.show_task_id){
        snprintf(flow_thread_info[thread_id].file_tbl_name, COMMON_FILE_PATH,
                                           "%s/%s_%06ld_http_n_%03d_%s.tbl",
                                           flow_thread_info[thread_id].file_tbl_dir,
                                           time_to_datetime(g_config.g_now_time),
                                           tv.tv_usec,thread_id,
                                           g_config.task_id);
    }
    else{
        snprintf(flow_thread_info[thread_id].file_tbl_name, COMMON_FILE_PATH,
                                           "%s/%s_%06ld_http_n_%03d.tbl",
                                           flow_thread_info[thread_id].file_tbl_dir,
                                           time_to_datetime(g_config.g_now_time),
                                           tv.tv_usec,thread_id);
    }

    flow_thread_info[thread_id].tbl_fp=fopen(flow_thread_info[thread_id].file_tbl_name, "w");
    if(NULL==flow_thread_info[thread_id].tbl_fp){
        printf("http tbl file:[%s] create error!\n",flow_thread_info[thread_id].file_tbl_name);
        return 0;
    }

    return 1;
}



static int _get_http_file_name(char *file_name, int thread_id, int diretion)
{
    if(NULL==file_name){
        return 0;
    }
    int ret=0;

    if(flow_thread_info[thread_id].tbl_fp==NULL){
        ret=_recreate_http_tbl_dir(thread_id);
        if(0==ret){
            return 0;
        }
    }else{
        if(flow_thread_info[thread_id].file_count>= g_config.log_max_num ||
           (flow_thread_info[thread_id].timeout_sec + g_config.write_tbl_maxtime < g_config.g_now_time)){
            if(flow_thread_info[thread_id].tbl_fp!=NULL){
                fclose(flow_thread_info[thread_id].tbl_fp);
                flow_thread_info[thread_id].tbl_fp=NULL;
            }
            char http_tbl_file[COMMON_FILE_PATH]={0};
            memcpy(http_tbl_file,flow_thread_info[thread_id].file_tbl_dir,strlen(flow_thread_info[thread_id].file_tbl_dir)-8);
            rename(flow_thread_info[thread_id].file_tbl_dir,http_tbl_file);
            ret=_recreate_http_tbl_dir(thread_id);
            if(0==ret){
                return 0;
            }
        }
    }

    if(g_config.show_task_id){
        snprintf(file_name, COMMON_FILE_PATH, "%s/%s_%llu_%ld_%03d_%s.http",
                               flow_thread_info[thread_id].file_tbl_dir,
                               diretion==FLOW_DIR_SRC2DST?"request":"response",
                               (unsigned long long)g_config.g_now_time_usec,
                               random(),
                               thread_id,
                               g_config.task_id);
    }
    else{
        snprintf(file_name, COMMON_FILE_PATH, "%s/%s_%llu_%ld_%03d.http",
                               flow_thread_info[thread_id].file_tbl_dir,
                               diretion==FLOW_DIR_SRC2DST?"request":"response",
                               (unsigned long long)g_config.g_now_time_usec,
                               random(),
                               thread_id);
    }
    return 1;
}

static void get_set_cookie_key(const uint8_t *key_start, int len, struct http_info *http_value) {
    while (len && key_start && isspace(*key_start)) {
        key_start++;
        len--;
    }

    if (!len || !key_start)
        return;

    const uint8_t *key = memchr(key_start, '=', len);
    if (key) {
        // key found
        int key_len = key - key_start;
        int min =
            ((int)sizeof(http_value->set_cookie_keys) - 1 > key_len ? key_len : (int)sizeof(http_value->set_cookie_keys) - 1);
        if ((int)(sizeof(http_value->set_cookie_keys) - strlen(http_value->set_cookie_keys)) > key_len) {
            strncat(http_value->set_cookie_keys, (char *)key_start, min);
            strcat(http_value->set_cookie_keys, " ");

            http_value->set_cookie_keys_num++;
        }
        int value_len = len - (key - key_start);
        int min_ = ((int)sizeof(http_value->set_cookie_values) - 1 > value_len ? value_len
                                                                               : (int)sizeof(http_value->set_cookie_values) - 1);
        if ((int)(sizeof(http_value->set_cookie_values) - strlen(http_value->set_cookie_values)) > key_len) {
            strncat(http_value->set_cookie_values, (char *)key + 1, min_);
            strcat(http_value->set_cookie_values, " ");
            http_value->set_cookie_values_num++;
        }
    }
}

static void get_cookie_key(const uint8_t *key_start, int len, struct http_info *http_value) {

    while (key_start && isspace(*key_start) && len)
    {
        key_start++;
        len --;
    }

    if (!key_start || !len)
        return;

    const uint8_t *key = memchr(key_start, '=', len);
    if (key) {
        // key found
        int key_len = key - key_start;
        int min = ((int)sizeof(http_value->cookie_keys) - 1 > key_len ? key_len : (int)sizeof(http_value->cookie_keys) - 1);
        if ((int)(sizeof(http_value->cookie_keys) - strlen(http_value->cookie_keys)) > key_len) {
            strncat(http_value->cookie_keys, (char *)key_start, min);
            strcat(http_value->cookie_keys, " ");

            http_value->cookie_keys_num++;
        }
    }
}
static void http_prase_set_cookies(const char * key, const struct header_value * _value,
                   struct http_info * http_info){
  if (key == NULL || _value == NULL)
    return;
   int k;
  for (k = 0; k < _value->len;) {
    const uint8_t *pair_end = memchr(_value->ptr + k, ';', _value->len);
    int pair_len = 0;
    if (pair_end) {
      // pref=id=2368504d
      pair_len = pair_end - (_value->ptr + k);
      get_set_cookie_key(_value->ptr + k, pair_len, http_info);
    } else {
      get_set_cookie_key(_value->ptr + k, _value->len - k, http_info);
      break;
    }
    k += pair_len + 1;
  }
}

static void
http_parse_cookies(const char * key, const struct header_value * _value,
                   struct http_info * http_value)
{
  if (key == NULL || _value == NULL)
    return;

  int k;
  for (k = 0; k < _value->len; ) {
    const uint8_t *pair_end = memchr(_value->ptr + k, ';', _value->len - k);
    int pair_len = 0;
    if (pair_end) {
      pair_len = pair_end - (_value->ptr + k);
      get_cookie_key(_value->ptr + k, pair_len, http_value);
    }
    else {
      get_cookie_key(_value->ptr + k, _value->len - k, http_value);
      break;
    }

    k += pair_len + 1;
  }
}
static void
http_collect_head_fields(const char * header, uint16_t header_len,
                 struct http_info *http_value)
{
  uint16_t max_len = sizeof(http_value->rsp_heads);
  size_t headers_len = 0;
  char * headers = NULL;

  if (http_value == NULL)
    return;

   if (http_value->is_request) {
    headers_len = strlen(http_value->req_heads);
    headers = &http_value->req_heads[0];
    if (header_len + headers_len + 1 > max_len)
      return;
    http_value->req_heads_num++;
  } else {
    headers_len = strlen(http_value->rsp_heads);
    headers = &http_value->rsp_heads[0];
    if (header_len + headers_len + 1 > max_len)
      return;
  }

  if (headers_len != 0) {
    strcat(headers, ",");
  }
  strncat(headers, header, header_len);

  gchar *h_md5 = g_compute_checksum_for_string(G_CHECKSUM_MD5, headers, strlen(headers));
  if (http_value->is_request) {
    memcpy(http_value->req_heads_md5, h_md5, strlen(h_md5));
  } else {
    memcpy(http_value->rsp_heads_md5, h_md5, strlen(h_md5));
  }
}




/*
* 解析http头部的每一行头域，寻找空行位置
*/
static int parse_http_line_info(const uint8_t *payload, const uint32_t payload_len, struct http_info *http_value) {
  int remain_len = payload_len;
  int offset = 0;
  uint8_t cookie = 0 , set_cookie = 0,vary = 0;

  if (payload_len == 0 || payload == NULL) {
    goto PARSE_END;
  }

  if (!http_value->table) {
    http_value->table = g_hash_table_new_full(g_str_hash, g_str_equal, _free_header, _free_key_value);
    if (http_value->table == NULL) {
      goto PARSE_END;
    }
  }
  const uint8_t *end_of_header = (uint8_t *)dpi_hybrid_strnstr((const char *)payload, payload_len, "\r\n\r\n");

  if (!end_of_header) {
    goto PARSE_END;
    //不够一个完整的head 下次解析
  }
  // http 头域 和  body 体之间的空行位置
  http_value->empty_line_position = end_of_header - payload + 2;
  const uint8_t *p = payload;
  uint16_t       parsed_lines = 0;
  while (p < end_of_header) {
    const uint8_t       *line_end = (uint8_t *)dpi_hybrid_strnstr((const char *)p, end_of_header + 2 - p, "\r\n");
    char                *key = NULL;
    size_t               key_len = 0;
    struct header_value *value = NULL;
    if (!line_end) {
      goto PARSE_END;
    }
    value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
    value->need_free = 0;

    if (parsed_lines == 0) {
      key = strdup("head_line");
      // key_len = strlen(key);
      value->len = line_end - p;
      value->ptr = (const uint8_t *)p;
      g_hash_table_insert(http_value->table, key, value);
      goto next_line;
    }

    uint8_t *colon_pos = (uint8_t *)strchr((const char *)p, ':');
    if (!colon_pos || colon_pos >= line_end) {
      //   DPI_LOG(DPI_LOG_DEBUG, "Invalid HTTP request: malformed heade line");
      goto next_line;
    }
    key_len = colon_pos - p;
    key = strndup((const char *)p, key_len);
    strdown_inplace(key);
    value->len = line_end - colon_pos - 1;
    value->ptr = (uint8_t *)colon_pos + 1;

    // 去除头部空格
    while (*value->ptr == ' ') {
      ++value->ptr;
      value->len -= 1;
    }
    if(g_config.http.http_exquisite_switch){
      http_collect_head_fields(key, key_len, http_value);
    }

    if (!strcmp(key, "host")) {
      http_value->host_num++;
    }  // add
    if (cookie == 0 && !strcmp(key, "cookie")) {
      http_parse_cookies(key, value, http_value);
      cookie = 1;
    }
    if(set_cookie == 0 && !strcmp(key,"set-cookie")){
      set_cookie =1;
      http_prase_set_cookies(key,value,http_value);
    }
    g_hash_table_insert(http_value->table, key, value);

  next_line:
    parsed_lines++;
    p = line_end + 2;
    offset = p - payload;
  }

PARSE_END:
  return offset;
}



static int dissect_http_content_disposition(struct http_info *http_value, const char *payload, int payload_len) {
  if (NULL == payload || !payload_len) {
    return -1;
  }

  const char *start = dpi_hybrid_strnstr((const char *)payload, payload_len, "filename");

  if (!start) {
    return -1;
  }
  start += strlen("filename");
  int remain_len = payload_len - (start - payload);
  for (int i = 0; i < remain_len; i++) {
    if (*(start + i) == '"') {
      start += i + 1;
      break;
    }
  }
  size_t      f_len = 0;
  const char *end = strpbrk(start, "\"\r\n\"");
  if (end) {
    f_len = end - start;
  } else {
    f_len = payload + payload_len - start;
  }
  char http_filename[512] = {0};
  if (f_len >= sizeof(http_filename)) {
    f_len = sizeof(http_filename) - 1;
  }
  strncpy(http_filename, start, f_len);
  if (strlen(http_filename) > 0) {
    char decode_uri[2048] = {0};
    uri_decode(http_filename, decode_uri, strlen(http_filename), sizeof(decode_uri));
    snprintf(http_value->http_filename, COMMON_FILE_PATH, "%s", decode_uri);
  }

  return 0;
}
struct write_position
{
    char *ptr;
    int *index;
    int unknow_line_num;
};

struct merge_unknown_line
{
    char unknown_line[1500];
    int index;
    int unknow_line_num;
};

static int write_http_unknown_reconds(char *log, int *idx, int max_len, const char *key, unsigned int key_len,
        const char *val, unsigned int val_len)
{
    unsigned int i;
    int index = *idx;

    for (i = 0; i < key_len; i++) {
        if (index >= max_len)
            break;

        if (key[i] == '|')
            log[index++] = '_';
        else if (key[i] == '\n' || key[i] == '\r' || key[i] == '&' || key[i] == '=')
            log[index++] = ' ';
        else
            log[index++] = key[i];
    }

    if (index < max_len)
        log[index++] = '=';

    for (i = 0; i < val_len; i++) {
        if (index >= max_len)
            break;

        if (val[i] == '|')
            log[index++] = '_';
        else if (val[i] == '\n' || val[i] == '\r' || val[i] == '&' || val[i] == '=')
            log[index++] = ' ';
        else
            log[index++] = val[i];
    }

    if (index + 1 < max_len) {
        log[index++] = '&';
    }

    *idx = index;
    return 0;
}

static void _merge_unknown_line(gpointer key, gpointer value, gpointer user_data)
{
    struct merge_unknown_line *position = (struct merge_unknown_line *)user_data;
    char *_key = (char *)key;
    struct header_value *_value = (struct header_value *)value;

    if (strlen(_key) > 0 || _value->len > 0) {
        write_http_unknown_reconds(position->unknown_line, &position->index, sizeof(position->unknown_line),
                    _key, strlen(_key), (const char *)_value->ptr, _value->len);
    }
    position->unknow_line_num++;
}

static void _get_http_client_server_port(struct flow_info *flow, const uint8_t *payload, uint32_t payload_len)
{
    if(payload==NULL || payload_len<7){
        return;
    }
    if (payload_len >= 7 && strncasecmp((const char *)payload, "HTTP/1.", 7) == 0) {
        //s2c
        flow->drt_port_src[FLOW_DIR_DST2SRC]=flow->port_src;
        flow->drt_port_dst[FLOW_DIR_DST2SRC]=flow->port_dst;
    }
    else if (payload_len >= 10) {
        for(int i = 0; HTTP_METHOD[i].method; i++){
            if(0 == strncasecmp((const char*)payload, HTTP_METHOD[i].method, HTTP_METHOD[i].len)){
                //c2s
                flow->drt_port_src[FLOW_DIR_SRC2DST]=flow->port_src;
                flow->drt_port_dst[FLOW_DIR_SRC2DST]=flow->port_dst;
                break;
            }
        }
    }
}

static int http_change_and_account_gbk(uint8_t *pData, int len)
{
    int loop = len;
    uint8_t *p = pData;
    while(loop > 0)
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            *p++ = ' ';
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }

        /* 参考: 中华人民共和国信息技术标准化技术委员会 汉字内码扩展规范(GBK) */
        /* GBK/3 CJK 汉字区 6080个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0X81) && ((unsigned char)p[0] <= 0XA0)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/1 GB2312符号,增补符号 717 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA1) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/5 扩充符号 166 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA8) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/4 CJK汉字和增补汉字 8160 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XAA) && ((unsigned char)p[0] <= 0XFE)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/2 GB2312汉字 6763 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XB0) && ((unsigned char)p[0] <= 0XF7)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        return p - pData;/* 这不是 GBK 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}
//add by xuxn 统计http解析post的总次数、成功率、失败率
extern volatile uint64_t post_total;    //http 解析.POST文件 总记录条数
extern volatile uint64_t post_success;    //http 解析.POST文件 解析成功的记录条数

static void gen_content_file_name(struct flow_info *flow, struct http_info *http_value, char *filename, int filename_len);
static int http_field_element(struct tbl_log *log_ptr, struct flow_info *flow, int direction, struct http_info *http_value, int *idx, int i)
{
    char local_filename[COMMON_FILE_PATH]={0};
    precord_t *record = log_ptr->record;
    struct http_session *session = (struct http_session *)flow->app_session;
    switch(i){
    case EM_HTTP_METHOD:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)http_value->PROTOCOL_VAL_PTR(method), http_value->PROTOCOL_VAL_LEN(method));
        break;
    case EM_HTTP_URI:
        if(session->uri!=NULL){
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->uri, strlen(session->uri));
        }else{
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        }
        break;
    case EM_HTTP_URI_COUNT:
        if (http_value->uri_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->uri_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_URI_KEYS:
        if (session->uri_keys_num > 0) {
            char _tmp[4096];
            int j;
            memset(_tmp, 0, sizeof(_tmp));
            for (j = 0; j < session->uri_keys_num; j++) {
                if ((int)(sizeof(_tmp) - strlen(_tmp)) < (int)strlen(session->uri_keys[j]))
                    break;

                strcat(_tmp, session->uri_keys[j]);
                strcat(_tmp, ",");
            }

            if (_tmp[strlen(_tmp)] == ',')
                _tmp[strlen(_tmp)] = '\0';
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, _tmp, strlen(_tmp));
        }
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_URI_KEYS_COUNT:
        if (session->uri_keys_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)session->uri_keys_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_URI_PATH:
        if (strlen(session->uri_path) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->uri_path, strlen(session->uri_path));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_URI_PATH_COUNT:
        if (session->uri_path_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)session->uri_path_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_VERSION:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)http_value->PROTOCOL_VAL_PTR(version), http_value->PROTOCOL_VAL_LEN(version));
        break;
    case EM_HTTP_STATUS:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)http_value->PROTOCOL_VAL_PTR(code), http_value->PROTOCOL_VAL_LEN(code));
        break;
    case EM_HTTP_RESPONSESTATUS:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)http_value->PROTOCOL_VAL_PTR(response_code), http_value->PROTOCOL_VAL_LEN(response_code));
        break;
    case EM_HTTP_CACHE_CONTROL:
        _find_hash_write_log_delete(http_value, "cache-control", record, idx);
        break;
    case EM_HTTP_CONNECTION:
        _find_hash_write_log_delete(http_value, "connection", record, idx);
        break;
    case EM_HTTP_COOKIE:
        _find_hash_write_log_delete(http_value, "cookie", record, idx);
        break;
    case EM_HTTP_COOKIE2:
        _find_hash_write_log_delete(http_value, "cookie2", record, idx);
        break;
    case EM_HTTP_COOKIE_KEYS:
        if (http_value->cookie_keys_num > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->cookie_keys, strlen(http_value->cookie_keys));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_DATE:
        _find_hash_write_log_delete(http_value, "date", record, idx);
        break;
    case EM_HTTP_PRAGMA:
        _find_hash_write_log_delete(http_value, "pragma", record, idx);
        break;
    case EM_HTTP_TRAILER:
        _find_hash_write_log_delete(http_value, "trailer", record, idx);
        break;
    case EM_HTTP_TRANSFER_ENCODING:
        _find_hash_write_log_delete(http_value, "transfer-encoding", record, idx);
        break;
    case EM_HTTP_UPGRADE:
        _find_hash_write_log_delete(http_value, "upgrade", record, idx);
        break;
    case EM_HTTP_VIA:
        _find_hash_write_log_delete(http_value, "via", record, idx);
        break;
    case EM_HTTP_VIA_COUNT:
        if (http_value->via_num != 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->via_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_WARNING:
        _find_hash_write_log_delete(http_value, "warning", record, idx);
        break;
    case EM_HTTP_ACCEPT:
        _find_hash_write_log_delete(http_value, "accept", record, idx);
        break;
    case EM_HTTP_ACCEPT_CHARSET:
        _find_hash_write_log_delete(http_value, "accept-charset", record, idx);
        break;
    case EM_HTTP_ACCEPT_ENCODING:
        _find_hash_write_log_delete(http_value, "accept-encoding", record, idx);
        break;
    case EM_HTTP_ACCEPT_LANGUAGE:
        _find_hash_write_log_delete(http_value, "accept-language", record, idx);
        break;
    case EM_HTTP_AUTHORIZATION:
//      _find_hash_write_log_delete(http_value, "authorization", record, idx);
        if ((int)strlen(session->auth) > 0) {
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->auth, strlen(session->auth));
            session->auth[0] = 0;
        }
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_AUTH_USERNAME:
        if (http_value->PROTOCOL_VAL_LEN(username) && http_value->PROTOCOL_VAL_PTR(username))
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (char *)http_value->PROTOCOL_VAL_PTR(username), http_value->PROTOCOL_VAL_LEN(username));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_EXPECT:
        _find_hash_write_log_delete(http_value, "expect", record, idx);
        break;
    case EM_HTTP_FROM:
        _find_hash_write_log_delete(http_value, "from", record, idx);
        break;
    case EM_HTTP_HOST:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->host, strlen(session->host));
        break;
    case EM_HTTP_HOST_COUNT:
        if (http_value->host_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->host_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_IF_MATCH:
        _find_hash_write_log_delete(http_value, "if-match", record, idx);
        break;
    case EM_HTTP_IF_MODIFIED_SINCE:
        _find_hash_write_log_delete(http_value, "if-modified-since", record, idx);
        break;
    case EM_HTTP_IF_NONE_MATCH:
        _find_hash_write_log_delete(http_value, "if-none-match", record, idx);
        break;
    case EM_HTTP_IF_RANGE:
        _find_hash_write_log_delete(http_value, "if-range", record, idx);
        break;
    case EM_HTTP_IF_UNMODIFIED_SINCE:
        _find_hash_write_log_delete(http_value, "if-unmodified-since", record, idx);
        break;
    case EM_HTTP_MAX_FORWARDS:
        _find_hash_write_log_delete(http_value, "max-forwards", record, idx);
        break;
    case EM_HTTP_PROXY_AUTHORIZATION:
        _find_hash_write_log_delete(http_value, "proxy-authorization", record, idx);
        break;
    case EM_HTTP_PROXY_TYPE:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->proxy_type, strlen(http_value->proxy_type));
        break;
    case EM_HTTP_PROXY_LOGIN:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->proxy_login, strlen(http_value->proxy_login));
        break;
    case EM_HTTP_PROXY_AUTHORIZATION_INFO:
        _find_hash_write_log_delete(http_value, "proxy-authentication-info", record, idx); //这个字段名称写错了,应该是 proxy-authentication-info 而不是 proxy-authorization-info
        break;
    case EM_HTTP_RANGE:
        _find_hash_write_log_delete(http_value, "range", record, idx);
        break;
    case EM_HTTP_REFERER:
        _find_hash_write_log_delete(http_value, "referer", record, idx);
        break;
    case EM_HTTP_TE:
        _find_hash_write_log_delete(http_value, "te", record, idx);
        break;
    case EM_HTTP_USER_AGENT:
        {
            if(http_value->table==NULL){
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
                break;
            }
            gpointer _value;
            struct header_value *value;
            _value = g_hash_table_lookup(http_value->table, "user-agent");
            value = (struct header_value *)_value;
            if (!value)
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            else
            {
                int i = 0, j = 0;
                char tmp[value->len*2 + 1];
                memset(tmp, 0, value->len*2 + 1);
                for(i=0; i < value->len && j < value->len*2 + 1; i++)
                {
                    if(!isprint(value->ptr[i]))
                    {
                        sprintf(tmp+j, "%2x", value->ptr[i]);
                        j++;
                    }
                    else
                        tmp[j] = value->ptr[i];
                    j++;
                }
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)tmp, strlen(tmp));
            }
            g_hash_table_remove(http_value->table, "user-agent");
        }
        break;
    case EM_HTTP_USER_AGENT_NUM:
        if (http_value->user_agent_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->user_agent_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_PROXY_PROXYAGENT:
        _find_hash_write_log_delete(http_value, "proxy-agent", record, idx);
        break;
    case EM_HTTP_USER_CPU:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->uaCPU, strlen(http_value->uaCPU));
        break;
    case EM_HTTP_USER_OS:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->uaOS, strlen(http_value->uaOS));
        break;

    case EM_HTTP_ACCEPT_RANGES:
        _find_hash_write_log_delete(http_value, "accept-ranges", record, idx);
        break;
    case EM_HTTP_AGE:
        _find_hash_write_log_delete(http_value, "age", record, idx);
        break;
    case EM_HTTP_ETAG:
        _find_hash_write_log_delete(http_value, "etag", record, idx);
        break;
    case EM_HTTP_LOCATION:
        _find_hash_write_log_delete(http_value, "location", record, idx);
        break;
    case EM_HTTP_PROXY_AUTHENTICATE:
        _find_hash_write_log_delete(http_value, "proxy-authenticate", record, idx);
        break;
    case EM_HTTP_INQUIRY_TYPE:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->inquiry_type, strlen(http_value->inquiry_type));
        break;
    case EM_HTTP_PROXY_CONNECT_HOST:
        _find_hash_write_log_delete(http_value, "proxy_connect_host", record, idx);
        break;
    case EM_HTTP_PROXY_CONNECT_PORT:
        _find_hash_write_log_delete(http_value, "proxy_connect_port", record, idx);
        break;
    case EM_HTTP_RETRY_AFTER:
        _find_hash_write_log_delete(http_value, "retry-after", record, idx);
        break;
    case EM_HTTP_SERVER:
        _find_hash_write_log_delete(http_value, "server", record, idx);
        break;
    case EM_HTTP_VARY:
        _find_hash_write_log_delete(http_value, "vary", record, idx);
        break;
    case EM_HTTP_WWW_AUTHENTICATE:
        _find_hash_write_log_delete(http_value, "www-authenticate", record, idx);
        break;
    case EM_HTTP_ALLOW:
        _find_hash_write_log_delete(http_value, "allow", record, idx);
        break;
    case EM_HTTP_CONTENT_ENCODING_S:
        _find_hash_write_log_delete(http_value, "content-encoding", record, idx);
        break;
    case EM_HTTP_CONTENT_LANGUAGE:
        _find_hash_write_log_delete(http_value, "content-language", record, idx);
        break;
    //不清除链表中的“content-length”，在后面删除
    case EM_HTTP_CONTENT_LENGTH:
        if (session->req_len)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, session->req_len);
        else if (session->rsp_len)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, session->rsp_len);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_CONTENT_LENGTH_REQ:
        if (session->req_len)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, session->req_len);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);

        if (g_hash_table_lookup(http_value->table, "content-length"))
        {
            g_hash_table_remove(http_value->table, "content-length");
        }
        break;
    case EM_HTTP_CONTENT_LENGTH_RSP:
        if (session->rsp_len)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, session->rsp_len);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);

        if (g_hash_table_lookup(http_value->table, "content-length"))
        {
            g_hash_table_remove(http_value->table, "content-length");
        }
        break;
    case EM_HTTP_CONTENT_LOCATION:
        _find_hash_write_log_delete(http_value, "content-location", record, idx);
        break;
    case EM_HTTP_CONTENT_MD5:
        _find_hash_write_log_delete(http_value, "content-md5", record, idx);
        break;
    case EM_HTTP_CONTENT_RANGE:
        _find_hash_write_log_delete(http_value, "content-range", record, idx);
        break;
    case EM_HTTP_CONTENT_TYPE:
        if (http_value->is_request)
            _find_hash_write_log_delete(http_value, "content-type", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_EXPIRES:
        _find_hash_write_log_delete(http_value, "expires", record, idx);
        break;
    case EM_HTTP_REFRESH:
        _find_hash_write_log_delete(http_value, "refresh", record, idx);
        break;
    case EM_HTTP_LAST_MODIFIED:
        _find_hash_write_log_delete(http_value, "last-modified", record, idx);
        break;
    case EM_HTTP_X_FORWARDED_FOR:
        _find_hash_write_log_delete(http_value, "x-forwarded-for", record, idx);
        break;
    case EM_HTTP_SET_COOKIE:
        if (http_value->set_cookie_keys[0])
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->set_cookie_keys, strlen(http_value->set_cookie_keys));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_SET_COOKIE_VALUE:
        if (http_value->set_cookie_values[0])
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->set_cookie_values, strlen(http_value->set_cookie_values));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_SET_COOKIE2:
        _find_hash_write_log_delete(http_value, "set-cookie2", record, idx);
        break;
    case EM_HTTP_X_POWERED_BY:
        _find_hash_write_log_delete(http_value, "x-powered-by", record, idx);
        break;
    case EM_HTTP_APPTYPE:
        //app_type
        if (http_value->app_type[0])
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->app_type, strlen(http_value->app_type));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;

    case EM_HTTP_X_HTTP_TOKEN:
        _find_hash_write_log_delete(http_value, "x-http-token", record, idx);
        break;
    case EM_HTTP_X_COOKIE_TOKEN:
        _find_hash_write_log_delete(http_value, "x-cookie-token", record, idx);
        break;
    case EM_HTTP_X_SINKHOLE:
        _find_hash_write_log_delete(http_value, "x-sinkhole", record, idx);
        break;
    case EM_HTTP_REQ_HEAD_FIELDS:
        if (strlen(http_value->req_heads) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->req_heads, strlen(http_value->req_heads));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_REQ_HEAD_FIELDS_MD5:
        if (strlen(http_value->req_heads_md5) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->req_heads_md5, strlen(http_value->req_heads_md5));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_REQ_HEAD_FIELDS_NUM:
        if (http_value->req_heads_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->req_heads_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_REQ_METHOD_NUM:
        if (http_value->req_method_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->req_method_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_REQ_VERSION_NUM:
        if (http_value->req_version_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->req_version_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_REQ_BODY:
        if (session->req_len)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->req_body, session->req_len);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_HEAD_FIELDS:
        if (strlen(http_value->rsp_heads) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->rsp_heads, strlen(http_value->rsp_heads));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_HEAD_FIELDS_MD5:
        if (strlen(http_value->rsp_heads_md5) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->rsp_heads_md5, strlen(http_value->rsp_heads_md5));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_CACHE_CONTROL:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "cache-control", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_CONNECTION:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "connection", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_PRAGMA:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "pragma", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_ACCEPT_RANGES:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "accept-ranges", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_ACCEPT_CHARSET:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "accept-charset", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_CONTENT_TYPE:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "content-type", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_VERSION:
        if (http_value->PROTOCOL_VAL_LEN(rsp_version) > 0)
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, http_field_array[i].type, http_value->PROTOCOL_VAL_PTR(rsp_version), http_value->PROTOCOL_VAL_LEN(rsp_version));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_BODY:
        if (session->rsp_len > 0 && session->rsp_body[0] != '\0') {
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->rsp_body, session->rsp_len);
        }
        else if (strlen(session->rsp_body) > 0) {//有http body 没有content-length
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->rsp_body, strlen(session->rsp_body));
        }
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_FULLTEXT_LEN:
        if (!http_value->is_request && http_value->rsp_fulltext_len > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->rsp_fulltext_len);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_URL:
        if (strlen(http_value->url) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->url, strlen(http_value->url));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
#ifdef DPI_SDT_ZDY
    case EM_HTTP_V51:
        if (log_ptr->content_ptr && strlen((const char *)log_ptr->content_ptr)>0 && memcmp(log_ptr->content_ptr,"HTTP_BODY_FORMAT=", strlen("HTTP_BODY_FORMAT=")) != 0) {
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char*)log_ptr->content_ptr, strlen((const char*)log_ptr->content_ptr));
        } else if (http_value->PROTOCOL_VAL_LEN(content))
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char*)http_value->PROTOCOL_VAL_PTR(content), strlen((const char*)http_value->PROTOCOL_VAL_PTR(content)));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_LOCAL_FILENAME:
        memset(local_filename, 0, sizeof(local_filename));
        if (g_config.http_content_limite) { //是否输出实体
            gen_content_file_name(flow, http_value, local_filename, sizeof(local_filename));
            char *suffix = strrchr(local_filename, '.');
            if(suffix && memcmp(suffix, ".bin", 4) == 0) { //uri中没有文件后缀的才按内容识别文件后缀
                if (log_ptr->content_ptr && strlen((const char *)log_ptr->content_ptr)>0 && memcmp(log_ptr->content_ptr,"HTTP_BODY_FORMAT=", strlen("HTTP_BODY_FORMAT=")) != 0) {
                    discern_filetype(log_ptr->content_ptr, strlen((char *)log_ptr->content_ptr), local_filename, sizeof(local_filename));
                } else {
                    discern_filetype(http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content), local_filename, sizeof(local_filename));
                }
            }
        }
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, local_filename, strlen(local_filename));

        if(strlen(local_filename)>0){
            FILE *fp = fopen(local_filename, "w");
            if(fp){
                if (log_ptr->content_ptr && strlen((const char *)log_ptr->content_ptr)>0 && memcmp(log_ptr->content_ptr,"HTTP_BODY_FORMAT=", strlen("HTTP_BODY_FORMAT=")) != 0) {
                    fwrite((const char *)log_ptr->content_ptr, strlen((char *)log_ptr->content_ptr), 1,fp);
                } else {
                    fwrite((const char *)http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content), 1,fp);
                }
                fclose(fp);
                fp = NULL;
            }
        }
        break;
    case EM_HTTP_CONDISP:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->conDisp, strlen(http_value->conDisp));
        break;
    case EM_HTTP_REMAINDERLINE:
        //避免在unknown_line中再次出现
        g_hash_table_remove(http_value->table, "head_line");
        //K00  merged unknown line
        struct merge_unknown_line unknown_line;
        memset(&unknown_line, 0, sizeof(unknown_line));
        g_hash_table_foreach(http_value->table, _merge_unknown_line, &unknown_line);
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, unknown_line.unknown_line, unknown_line.index>0?(unknown_line.index - 1):0);
        break;
#endif
    default:
        write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    }

    return 0;
}

int decode_http_v51(struct flow_info *flow, struct http_info *http_value, struct tbl_log *log_ptr)
{
    const char *str = NULL;
    if (http_value->PROTOCOL_VAL_LEN(content) <= 0)
    {
        return 0;
    }

    int content_size = 1024*1024*10; //限定10M
    log_ptr->content_ptr = malloc(content_size);
    memset(log_ptr->content_ptr, 0, content_size);

    str = filetype((const char *)http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content));
    if(str)
    {
        snprintf((char *)log_ptr->content_ptr, content_size, "HTTP_BODY_FORMAT=%s&LEN=%d", str, http_value->PROTOCOL_VAL_LEN(content));
    }
    else if(http_value->chunk_flag == 1)//避免post数据经YV_HttpPostParse_ParseRAW处理数据量减少
    {
        int content_ptr_len = YV_HttpPostParse_ParseRAW(NULL, (const char *)http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content),
                (char *)log_ptr->content_ptr, content_size);
         //解析后可识别文件格式且是非字符串
         str = filetype((const char *)log_ptr->content_ptr, content_ptr_len);
         if(content_ptr_len>0 && str) {
            http_value->PROTOCOL_VAL_PTR(content) = NULL; //触发HTTP_VALUE_DUP重新申请空间
            HTTP_VALUE_DUP(flow, log_ptr->content_ptr, content_ptr_len, http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content));
            snprintf((char *)log_ptr->content_ptr, content_size, "HTTP_BODY_FORMAT=%s&LEN=%d", str, content_ptr_len);
         }
    }
    return 0;
}

static int write_http_log(struct flow_info *flow, int direction,  struct http_info *http_value, SdtMatchResult *match_result)
{
    int idx = 0,i=0;
    struct tbl_log *log_ptr=NULL;
    const char *str= NULL;

    if(!http_value){
        return PKT_DROP;
    }

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);

    log_ptr->content_ptr = NULL;

#ifdef DPI_SDT_ZDY
    decode_http_v51(flow, http_value, log_ptr);
#endif

    idx = 0;
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "http");
    for(i=0; i<EM_HTTP_MAX;i++){
        http_field_element(log_ptr, flow, direction, http_value, &idx, i);
    }

#ifndef DPI_SDT_ZDY
    //新增筛选非必须字段
    precord_put_to_layer(log_ptr->record, "http", "uriSearch", string, http_value->uriSearch);
    if (http_value->is_request == 1) {
        precord_put_to_layer(log_ptr->record, "http", "conDispUp", string, http_value->conDisp);
    } else {
        precord_put_to_layer(log_ptr->record, "http", "conDispDown", string, http_value->conDisp);
    }
    precord_put_to_layer(log_ptr->record, "http", "imsi", string, http_value->imsi);

    precord_put_to_layer(log_ptr->record, "http", "imei", string, http_value->imei);
#endif
    //record_show(log_ptr->record);
    log_ptr->log_type    = TBL_LOG_HTTP;
    log_ptr->log_len     = idx;
    log_ptr->thread_id   = flow->thread_id;
    log_ptr->flow        = flow;

    if(log_ptr->content_ptr)
    {
        free(log_ptr->content_ptr);
        log_ptr->content_ptr = NULL;
    }

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

static int write_http_record_log(struct flow_info *flow, precord_t *record) {
    struct tbl_log *log_ptr = NULL;
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    init_log_ptr_data(log_ptr, flow, EM_TBL_LOG_ID_BY_DEFAULT);
    log_ptr->content_ptr = NULL;
    log_ptr->record = record;
    //record_show(log_ptr->record);
    log_ptr->log_type = TBL_LOG_HTTP;
    log_ptr->thread_id = flow->thread_id;
    log_ptr->flow = flow;

    if (log_ptr->content_ptr) {
        free(log_ptr->content_ptr);
        log_ptr->content_ptr = NULL;
    }

    if (write_tbl_log(log_ptr) != 1) {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

static int http_record_put_value(struct flow_info *flow, int direction, struct tbl_log *log_ptr,struct http_info *http_value){
      int idx = 0,i=0;

      write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);

#ifdef DPI_SDT_ZDY
    decode_http_v51(flow, http_value, log_ptr);
#endif

    idx = 0;
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "http");
    for(i=0; i<EM_HTTP_MAX;i++){
        http_field_element(log_ptr, flow, direction, http_value, &idx, i);
    }

#ifndef DPI_SDT_ZDY
    //新增筛选非必须字段
    precord_put_to_layer(log_ptr->record, "http", "uriSearch", string, http_value->uriSearch);
    if (http_value->is_request == 1) {
        precord_put_to_layer(log_ptr->record, "http", "conDispUp", string, http_value->conDisp);
    } else {
        precord_put_to_layer(log_ptr->record, "http", "conDispDown", string, http_value->conDisp);
    }

#endif

  return 0;
}

static precord_t *http_creat_record(struct flow_info *flow, int direction, struct http_info *http_value) {
  if (NULL == flow->app_session) {
    return NULL;
  }
  int idx = 0,i=0;

  struct http_session *session = (struct http_session *)flow->app_session;
  precord_t           *record = NULL;

  ya_fvalue_t *value;
  uint8_t      str[1024];  // 字段数据
  dpi_precord_new_record(record, NULL, NULL);
  struct tbl_log log_ptr;
  log_ptr.record = record;

  http_record_put_value(flow,direction,&log_ptr,http_value);
  return record;
}

// 辅助函数：检查是否匹配关键字（不区分大小写）
static int check_keyword(const char *str, size_t len, const char *keyword) {
  size_t keyword_len = strlen(keyword);
  if (len < keyword_len)
    return 0;
  return strncasecmp(str, keyword, keyword_len) == 0;
}

// 辅助函数：查找token结束位置
static const char *find_token_end(const char *start, size_t remaining_len) {
  for (size_t i = 0; i < remaining_len; i++) {
    if (start[i] == ';' || start[i] == ')' || start[i] == '\r' || start[i] == '\n') {
      return start + i;
    }
  }
  return start + remaining_len;
}

/*************************************************************************************************/
static uint8_t *find_next_boundary(
    struct http_info *http_value, const uint8_t *boundary_content, int boundary_content_len) {
  if (boundary_content_len < 0 || boundary_content == NULL) {
    return NULL;
  }
  // 从每一行开始 找到下一个 --$(boundary)
  int      payload_len = boundary_content_len;
  int      boundary_len = http_value->boundary_key_len;
  uint8_t *line_start = (uint8_t *)boundary_content;
  //针对每段开头处理 找到 boundary 的那一行
  while (line_start != NULL) {
    // 此行开头是 --$(boundary)
    if (*line_start == '-' && *(line_start + 1) == '-' &&
        (0 == memcmp(line_start, http_value->boundary, http_value->boundary_key_len) ||
            0 == memcmp(line_start + 2, http_value->boundary, http_value->boundary_key_len))) {
      return line_start;
    }
    int line_offset = _find_single_line_end(line_start, payload_len - (line_start - boundary_content));
    // 还有下一行
    if (line_offset >= 0 && line_offset < payload_len - (line_start - boundary_content)) {
      line_offset += 2;
      line_start += line_offset;
    } else {
      //这一行是最后一行了!
      line_start = NULL;
    }
  }
  return NULL;
}

static int http_parser_content_disposition_header(
    struct http_info *http_value, const uint8_t *boundary_header, int header_len) {
  //解析boundary头
  // ---------------------------************
  // Content-Disposition: form-data; name="uuid";
  // fd2356c3-12a3-4b56-86c7-89012d345678
  char *content_disposition = NULL;
  int   content_disposition_len = 0;
  // 解析Content-Disposition
  get_data_key_value(
      (const char *)boundary_header, header_len, "Content-Disposition: ", "\r\n", &content_disposition, &content_disposition_len);
  if (content_disposition_len > 0 && (content_disposition_len + strlen(http_value->conDisp) + 1) < sizeof(http_value->conDisp)) {
    if (strlen(http_value->conDisp) > 0) {
      strcat(http_value->conDisp, ",");
    }
    memcpy(http_value->conDisp + strlen(http_value->conDisp), content_disposition, content_disposition_len);
  }
  get_data_key_value(
      (const char *)boundary_header, header_len, " filename=\"", "\"\r\n", &content_disposition, &content_disposition_len);
  if (content_disposition_len > 0) {
    if (strlen(http_value->http_filename) > 0) {
      //在url中拿到过一次文件名了！但是boundary中是实际的文件名·
      memset(http_value->http_filename, 0, sizeof(http_value->http_filename));
    }
    memcpy(http_value->http_filename, content_disposition,
        content_disposition_len > (int)sizeof(http_value->http_filename) ? (int)sizeof(http_value->http_filename)
                                                                        : content_disposition_len);
    return 1;
  }
  return 0;
}

void dissect_uri_form_urlencoded(void* user,char* key,int key_len,char* value,int val_len){
  USER * user_ = user;

  struct http_info *http_value = user_->http_value;
  int copy_len = val_len > (int)sizeof(http_value->imsi) ? (int)(sizeof(http_value->imsi) - 1) : val_len;
  if (strncmp(key, "imsi", 4) == 0) {
      memcpy(http_value->imsi, value, copy_len);
  } else if (strncmp(key, "imei", 4) == 0) {
      memcpy(http_value->imei, value, copy_len);
  }
}

static void http_parse_form_urlencoded(struct http_info *http_value,const uint8_t *content, int content_len){
    char *p_param = (char*)content;
    if (!p_param){
        return;
  }
  USER user;
  user.session = NULL;
  user.http_value = http_value;
  get_parameters_keys_values(p_param,content_len,&user,dissect_uri_form_urlencoded);
}

void set_boundary_key_info(struct http_info *http_value, char *bounday_ptr, int boundary_len) {
  memcpy(http_value->boundary, bounday_ptr, boundary_len);
  http_value->boundary_key_len = boundary_len;
}

#define CHUNK_SIZE_MAX 32

static void http_parser_chunked_content(const uint8_t *chunked_content, uint32_t chunked_content_len,
        uint8_t *content, uint32_t *content_len)
{
    uint32_t offset = 0;
    int linelen;
    uint32_t max_len = *content_len;
    char chunk_string[CHUNK_SIZE_MAX];
    uint32_t chunk_size;

    *content_len = 0;

    while (offset < chunked_content_len) {
        linelen = find_packet_line_end(chunked_content + offset, chunked_content_len - offset);
        if (linelen <= 0 || linelen >= CHUNK_SIZE_MAX) {
            /* Can't get the chunk size line */
            break;
        }

        strncpy(chunk_string, (const char *)chunked_content + offset, linelen);
        chunk_string[linelen] = 0;
        chunk_size = strtol(chunk_string, NULL, 16);

        offset += linelen + 2;
        if (chunk_size > chunked_content_len - offset) {
            chunk_size = chunked_content_len - offset;
        }

        if (*content_len + chunk_size < max_len) {
            memcpy(content + *content_len, chunked_content + offset, chunk_size);
            *content_len += chunk_size;
        }

        offset += chunk_size + 2;
        if (offset >= chunked_content_len)
            break;

        if (chunk_size == 0)
            break;
    }

    return;
}

#define GET_DEV_INFO(devArr, arrlen, devFlag)    do{                                    \
    char* tmp0 = strcasestr((char *)value->ptr, (devFlag));                            \
    if(NULL != tmp0)                                                                \
    {                                                                                \
        char* tmp1 = strstr(tmp0, ";");                                                \
        char* tmp2 = strstr(tmp0, ")");                                                \
        if(NULL != tmp2 && (tmp1 == NULL || tmp1 > tmp2))                            \
            memcpy((devArr), tmp0, (tmp2-tmp0<(arrlen) ? tmp2-tmp0:(arrlen)-1));    \
        else if(NULL != tmp2 && NULL != tmp1 && tmp1 < tmp2)                        \
            memcpy((devArr), tmp0, (tmp1-tmp0<(arrlen) ? tmp1-tmp0:(arrlen)-1));    \
        else if(NULL == tmp1 && NULL == tmp2)                                        \
        {                                                                            \
            tmp1 = strstr(tmp0,"\r\n");                                                \
            if(NULL != tmp1)                                                        \
                memcpy((devArr), tmp0, (tmp1-tmp0<(arrlen) ? tmp1-tmp0:(arrlen)-1));\
        }                                                                            \
    }                                                                                \
}while(0)

size_t ws_base64_decode_inplace(char *s)
{
    static const char b64[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\r\n";
    int bit_offset, byte_offset, idx, i;
    unsigned char *d = (unsigned char *)s;
    char *p;
    int  cr_idx;
    // we will allow CR and LF - but ignore them
    cr_idx = (int)(strchr(b64, '\r') - b64);
    i = 0;
    while (*s && (p = strchr(b64, *s))) {
        idx = (int)(p - b64);
        if (idx < cr_idx) {
            byte_offset = (i * 6) / 8;
            bit_offset = (i * 6) % 8;
            d[byte_offset] &= ~((1 << (8 - bit_offset)) - 1);
            if (bit_offset < 3) {
                d[byte_offset] |= (idx << (2 - bit_offset));
            } else {
                d[byte_offset] |= (idx >> (bit_offset - 2));
                d[byte_offset + 1] = 0;
                d[byte_offset + 1] |= (idx << (8 - (bit_offset - 2))) & 0xFF;
            }
            i++;
        }
        s++;
    }
    d[i*3/4] = 0;
    return i*3/4;
}
static int check_passowrd(const char *name, const char *password) {
  if (name == NULL || password == NULL)
    return 0;
  if (strlen(name) == 0 || strlen(password) == 0)
    return 0;
  if (strlen(name) > 32 || strlen(password) > 32)
    return 0;
  // if name or passwrd contains "(null)", return
  if (strstr(name, "(null)") || strstr(password, "(null)"))
    return 0;
  return 1;
}
static void http_free_session_value(struct http_session *session) {
  if (session->uri)
    free(session->uri);
  if (session->cache[0].cache)
    free(session->cache[0].cache);
  if (session->cache[1].cache)
    free(session->cache[1].cache);
}

static int http_write_file_log(struct http_file *file) {
    //未知完成状态的文件 留在conv超时中关闭
  if (file->record) {
    precord_put_to_layer(file->record, "http", "localFilename", string, file->local + g_config.tbl_out_dir_len + 1);
    precord_put_to_layer(file->record, "http", "localFilepath", string, file->local);

    write_http_record_log(file->flow, file->record);
    file->record = NULL;
  }
  dpi_flow_free(file->flow, dpi_flow_timeout_free);
  return 0;
}


static int64_t http_get_chunkedlen_from_body(const char *payload, int payload_len, uint64_t *el) {
  int64_t     offset = 0;
  int64_t     chunk_size = 0;
  int64_t     chunk_len = 0;
  const char *p = NULL;

  offset = 0;

  // while (offset < payload_len) {
  chunk_len = find_packet_line_end((const uint8_t *)payload + offset, (uint32_t)payload_len - offset);
  if (chunk_len <= 0) {
    return chunk_size;
  }

  chunk_size = strtol(payload + offset, NULL, 16);

  offset += (chunk_len + 2 + chunk_size + 2);  // point next chunked
  if (offset > payload_len) {
    //下一个chunk头不在当前帧内
    return chunk_size;
  } else if (0 == chunk_size && offset == payload_len) {
    // chunk结束标识
    return 0;
    // }
  }
  return chunk_size;
}

static void http_freame_chunked_reset(struct http_session *session) {
  session->chunked_state = HTTP_CHUNKED_STATE_UNKNOWN;
  session->http_chunked_len = 0;
}

static void http_session_chunked_reset(struct http_session *session) {
  http_freame_chunked_reset(session);
  session->write_offset = 0;
  session->http_chunked_write_len = 0;
  session->body_state = HTTP_BODY_TYPE_CHUNKED;
}

static int dissect_http_range(struct flow_info *flow, gpointer _value) {
  // <unit> <range-start>-<rang-end>/<size>    Content-Range: bytes=0-21069080
  struct header_value *value = (struct header_value *)_value;
  if (!value->ptr) {
    return -1;
  }
  struct http_session *session = flow->app_session;
  struct http_range   *range = &session->range;

  char temp[256] = {0};
  strncpy(temp, (const char *)value->ptr, value->len > sizeof(temp) - 1 ? sizeof(temp) - 1 : value->len);
  // printf("[%s]", temp);
  // 解析unit部分
  char *unit_end = strchr(temp, '=');
  if (!unit_end)
    return -1;
  *unit_end = '\0';
  strncpy(range->unit, temp, sizeof(range->unit) - 1);
  range->unit[sizeof(range->unit) - 1] = '\0';

  char *remaining = unit_end + 1;

  char *range_part = remaining;

  // 解析range部分
  if (strcmp(range_part, "*") == 0) {
    range->has_range = false;
    range->start = range->end = -1;
  } else {
    char *dash = strchr(range_part, '-');
    if (!dash)
      return -1;
    *dash = '\0';

    char *endptr;
    range->start = strtol(range_part, &endptr, 10);
    if (*endptr != '\0')
      return -1;

    range->end = strtol(dash + 1, &endptr, 10);
    if (*endptr != '\0')
      return -1;

    range->has_range = true;
  }

  session->body_state = HTTP_BODY_TYPE_RANGE;
  // printf("\ndissect http range start [%d] end [%d]\n", range->start, range->end);
  return 1;
}

static int dissect_http_content_range(struct flow_info *flow, gpointer _value) {
  // <unit> <range-start>-<rang-end>/<size>    Content-Range: bytes 0-21069080/21069081
  // <unit> <range-start>-<rang-end>/*         Content-Range: bytes 42-153466132/*          [长度未知]
  // <unit> */<size>                           Content-Range: bytes */1234                  [请求的范围不满足服务器中的内容]
  struct header_value *value = (struct header_value *)_value;
  if (!value->ptr) {
    return -1;
  }
  struct http_session *session = flow->app_session;
  struct http_range   *range = &session->range;

  char temp[256] = {0};
  strncpy(temp, (const char *)value->ptr, value->len > sizeof(temp) - 1 ? sizeof(temp) - 1 : value->len);

  // strncpy(temp, (const char *)value->ptr, value->len);
  // printf("[%s]", temp);

  // 解析unit部分
  char *unit_end = strchr(temp, ' ');
  if (!unit_end)
    return -1;
  *unit_end = '\0';
  strncpy(range->unit, temp, sizeof(range->unit) - 1);
  range->unit[sizeof(range->unit) - 1] = '\0';

  char *remaining = unit_end + 1;

  // 分割range/size部分
  char *slash = strchr(remaining, '/');
  if (!slash)
    return -1;
  *slash = '\0';
  char *range_part = remaining;
  char *size_part = slash + 1;

  // 解析range部分
  if (strcmp(range_part, "*") == 0) {
    range->has_range = false;
    range->start = range->end = -1;
  } else {
    char *dash = strchr(range_part, '-');
    if (!dash)
      return -1;
    *dash = '\0';

    char *endptr;
    range->start = strtol(range_part, &endptr, 10);
    if (*endptr != '\0')
      return -1;

    range->end = strtol(dash + 1, &endptr, 10);
    if (*endptr != '\0')
      return -1;

    range->has_range = true;
  }

  // 解析size部分
  if (strcmp(size_part, "*") == 0) {
    range->size_known = false;
    range->size = -1;
  } else {
    char *endptr;
    range->size = strtol(size_part, &endptr, 10);
    if (*endptr != '\0')
      return -1;
    range->size_known = true;
  }

  session->body_state = HTTP_BODY_TYPE_RANGE;
  // printf("\ndissect http content range start [%d] end [%d]\n", range->start, range->end);

  return 1;
}


static void dissect_http_authorization(struct http_info *http_value, struct http_session *session)
{
    struct header_value *value = (struct header_value *)g_hash_table_lookup(http_value->table, "proxy-authorization");
    if(!value)
        value = (struct header_value *)g_hash_table_lookup(http_value->table, "authorization");
    if(value)
    {
        int copy_len = value->len >= sizeof(session->auth) ? sizeof(session->auth) - 1 : value->len;
        memcpy(session->auth, value->ptr, copy_len);
        session->auth[copy_len] = 0;
        const uint8_t *tmp = memchr(value->ptr, ' ', value->len);
        if(tmp)
        {
            int len = tmp - value->ptr;
            const uint8_t *head;
            size_t login_len;
            memcpy(http_value->proxy_type,  value->ptr, DPI_MIN(len, TYPE_LEN-1));
            if(strncasecmp(http_value->proxy_type, "Basic", 5)  == 0){
                memcpy(http_value->proxy_login, tmp + 1,    DPI_MIN(value->len - len - 1, LOGIN_LEN-1));
                login_len = ws_base64_decode_inplace(http_value->proxy_login);
                head = memchr(http_value->proxy_login, ':', login_len);
                if (head) {
                    http_value->username_val_ptr = (const uint8_t*)http_value->proxy_login;
                    http_value->username_val_len = head - (const uint8_t*)http_value->proxy_login;
                }
            }
            else if (strncasecmp(http_value->proxy_type, "Digest", 6) == 0) {
                if ((head = memstr(tmp, " username=\"", 11))) {
                    tmp += 11;
                    if ((head = memstr(tmp, "\"", value->len - (tmp - value->ptr)))) {
                        http_value->username_val_ptr = tmp;
                        http_value->username_val_len = head - tmp;
                    }
                }
            }
        }
    }
}
static void dissect_http_authenticate(struct http_info *http_value) {
  //质询认证方案："basic" 的截取
  struct header_value *value = (struct header_value *)g_hash_table_lookup(http_value->table, "www-authenticate");
  if (!value)
    value = g_hash_table_lookup(http_value->table, "proxy-authenticate");
  if (value) {
    const uint8_t *tmp = memchr(value->ptr, ' ', value->len);
    if (tmp) {
      int len = tmp - value->ptr;
      memcpy(http_value->inquiry_type, value->ptr, len >= TYPE_LEN ? TYPE_LEN - 1 : len);
    }
  }
}




static void dissect_http_user_agent(struct http_info *http_value) {
  struct header_value *value = (struct header_value *)g_hash_table_lookup(http_value->table, "user-agent");
  if (!value) {
    return;
  }

  http_value->user_agent_num++;

  const char *ptr = (const char *)value->ptr;
  size_t      len = value->len;
  const char *devFlags[] = {"linux", "windows ", "windows/", "iphone ", "android", "mac","Macintosh",NULL};

  // 初始化缓冲区
  http_value->uaCPU[0] = '\0';
  http_value->uaOS[0] = '\0';

  for (size_t i = 0; i < len;) {
    // 检查CPU信息
    if (!http_value->uaCPU[0] && i + 2 < len && check_keyword(ptr + i, len - i, "cpu")) {
      const char *token_start = ptr + i;
      const char *token_end = find_token_end(token_start, len - i);
      size_t      copy_len = MIN(token_end - token_start, CPU_LEN - 1);
      memcpy(http_value->uaCPU, token_start, copy_len);
      http_value->uaCPU[copy_len] = '\0';
      i += (token_end - token_start);
      continue;
    }

    // 检查OS信息
    if (!http_value->uaOS[0]) {
      for (int j = 0; devFlags[j]; j++) {
        size_t flag_len = strlen(devFlags[j]);
        if (i + flag_len < len && check_keyword(ptr + i, len - i, devFlags[j])) {
          const char *token_start = ptr + i;
          const char *token_end = find_token_end(token_start, len - i);
          size_t      copy_len = MIN(token_end - token_start, OS_LEN - 1);
          memcpy(http_value->uaOS, token_start, copy_len);
          http_value->uaOS[copy_len] = '\0';
          i += (token_end - token_start);
          break;
        }
      }
    }

    // 如果已经找到所有信息，提前退出
    if (http_value->uaCPU[0] && http_value->uaOS[0]) {
      break;
    }

    i++;
  }
}

static void get_http_host(struct http_info *http_value, struct http_session *session) {
  //缓存host字段到session中
  struct header_value *value = (struct header_value *)g_hash_table_lookup(http_value->table, "host");
  if (value) {
    size_t copy_len = value->len >= sizeof(session->host) ? sizeof(session->host) - 1 : value->len;
    memcpy(session->host, value->ptr, copy_len);
    session->host[copy_len] = 0;
    g_hash_table_remove(http_value->table, "host");

    //雅虎认证
    if (copy_len > 9 && strcmp(session->host + copy_len - 9, "yahoo.com") == 0) {
      value = g_hash_table_lookup(http_value->table, "head_line");
      if (!memchr(value->ptr, '?', value->len))
        return;
      const uint8_t *login = (const uint8_t *)memstr((const uint8_t *)value->ptr, "login=", value->len);
      const uint8_t *passwd = (const uint8_t *)memstr((const uint8_t *)value->ptr, "passwd=", value->len);
      if (login && passwd) {
        uint8_t *login_end = memchr(login, '&', value->len);
        uint8_t *passwd_end = memchr(passwd, '&', value->len);
        if (login_end && passwd_end) {  //FORMAT   login:passwd
          strcpy(http_value->proxy_type, "Yahoo");
          uint8_t off = DPI_MIN(login_end - login - 6, LOGIN_LEN - 1);
          memcpy(http_value->proxy_login, login + 6, off);
          if (off < LOGIN_LEN - 1)
            http_value->proxy_login[off++] = ':';
          if (off + passwd_end - passwd - 7 < LOGIN_LEN - 1)
            memcpy(http_value->proxy_login + off, passwd + 7, passwd_end - passwd - 7);
        }
      }
    }
  }
}

void uri_decode(char *src, char *dst, int src_len, int dst_len) {
  char a, b;
  int  len = src_len;
  if (src_len > dst_len)
    return;
  while (len >= 0) {
    if (dst_len == 1) {
      // 1 for '\0'
      break;
    }
    if ((*src == '%') && ((a = src[1]) && (b = src[2])) && (isxdigit(a) && isxdigit(b))) {
      if (a >= 'a')
        a -= 'a' - 'A';
      if (a >= 'A')
        a -= 'A' - 10;
      else
        a -= '0';
      if (b >= 'a')
        b -= 'a' - 'A';
      if (b >= 'A')
        b -= 'A' - 10;
      else
        b -= '0';
      *dst++ = 16 * a + b;
      src += 3;
      len -= 3;
      dst_len--;
    } else if (*src == '+') {
      *dst++ = ' ';
      src++;
      len--;
      dst_len--;
    } else {
      *dst++ = *src++;
      len--;
      dst_len--;
    }
  }
  *dst++ = '\0';
}

static void get_http_uri(struct http_info *http_value, struct http_session *session) {
    if (session == NULL) {
      return;
    }
    if (http_value->PROTOCOL_VAL_LEN(uri) == 0) {
      return;
    }

    size_t copy_len = http_value->PROTOCOL_VAL_LEN(uri);

    if (session->uri != NULL) {
      free(session->uri);
      session->uri = NULL;
      // printf("\nHTTP get url free url[session : %p]\n", session);
    }

    session->uri = malloc(copy_len + 1);
    if (session->uri == NULL) {
      DPI_LOG(DPI_LOG_ERROR, "malloc failed!");
      return;
    }
    memset(session->uri, 0, copy_len + 1);
    char decode_uri[2048] = {0};
    uri_decode((char *)http_value->PROTOCOL_VAL_PTR(uri), decode_uri, copy_len - 1, sizeof(decode_uri));
    if (decode_uri[0] == '0') {
      strncpy(session->uri, (const char *)http_value->PROTOCOL_VAL_PTR(uri), copy_len);
      return;
    }
    copy_len = strlen(decode_uri);
    session->uri = realloc(session->uri, copy_len + 1);
    strncpy(session->uri, decode_uri, copy_len);
    http_value->uri_num++;  // uri 数量
    char *p_param = memchr(session->uri, '?', copy_len);

    // 获取uri path
    {
        //先初始化，避免因历史数据产生脏数据
        memset(session->uri_path, 0, sizeof(session->uri_path));
        if (p_param) {
            memcpy(session->uri_path, session->uri, p_param - session->uri);
        }
        else {
            memcpy(session->uri_path, session->uri, copy_len);
        }

        session->uri_path_num++;
    }
}

static void get_http_method(struct http_info *http_value, struct http_session *session) {
  if (session == NULL) {
    return;
  }
  if (http_value->PROTOCOL_VAL_LEN(method) == 0 || http_value->PROTOCOL_VAL_PTR(method) == NULL) {
    return;
  }

  memset(session->method, 0, sizeof(session->method));

  size_t copy_len = http_value->PROTOCOL_VAL_LEN(method) > sizeof(session->method) ? sizeof(session->method) - 1
                                                                                  : http_value->PROTOCOL_VAL_LEN(method);

  memcpy(session->method, http_value->PROTOCOL_VAL_PTR(method), copy_len);
  session->method[copy_len] = 0;
}

static void get_http_username_password(struct http_info *http_value, struct http_session *session) {
  if (http_value == NULL || session == NULL)
    return;
  if (strlen(http_value->proxy_login)) {
    char *tmp = (char *)memchr(http_value->proxy_login, ':', strlen(http_value->proxy_login));
    if (tmp) {
      uint32_t username_len = tmp - http_value->proxy_login;
      memcpy(session->username, http_value->proxy_login,
          username_len > sizeof(session->username) ? sizeof(session->username) - 1 : username_len);
      uint32_t password_len = strlen(http_value->proxy_login) - username_len - 1;
      memcpy(session->password, tmp + 1, password_len > sizeof(session->password) ? sizeof(session->password) - 1 : password_len);
      return;
    }
  }

  memset(session->username, 0, sizeof(session->username));
  memset(session->password, 0, sizeof(session->password));

  return;
}


static void geturiSearch_v2(struct http_info *http_value,struct http_session * session,uint8_t is_http_request,char* key,int key_len,char* value ,int value_len){
  //只取请求中的key
  if(!http_value->is_request && http_value->is_search >= 0)
      return;
  //如果是搜索引擎
  if(http_value->is_search == 0){
    for(int i = 0; searchHost[i]!= NULL; i++){
        if(NULL!= strstr(session->host , searchHost[i])){
          http_value->is_search = 1;
          break;
        }
        if(NULL == searchHost[i+1]){
          http_value->is_search = -1;
          // 只对host进行一次匹配
          return;
        }
    }
  }
  if(http_value->is_search == 2){
    // 已经找到
    // 暂时只进行一次赋值，如有要求再取消该条件
    return;
  }
  for(int i =0; searchKey[i]!= NULL; i++){
    char*str = strstr(key, searchKey[i]);
    if (str!= NULL && (str-key)< key_len) {

      if(0 == strlen(http_value->uriSearch )){
        value_len = DPI_MIN(value_len, (int)sizeof(http_value->uriSearch)-1);
        memcpy(http_value->uriSearch , value , value_len);
      }else {
        http_value->is_search = 2;
        value_len = DPI_MIN(value_len, (int)(sizeof(http_value->uriSearch) - (int)strlen(http_value->uriSearch) - 2));
        if (value_len <= 0) break;
        strcat(http_value->uriSearch, ",");
        strncat(http_value->uriSearch, value, value_len);
      }
    }
  }
}

static void geturiSearch(struct http_info *http_value,struct http_session * session,uint8_t is_http_request,char* uri_key,char* p_start,char*p_next,int copy_len){
  //只取请求中的key
  if(!is_http_request && http_value->is_search >= 0)
      return;
  //如果是搜索引擎
  if(http_value->is_search == 0){
    for(int i = 0; searchHost[i]!= NULL; i++){
        if(NULL!= strstr(session->host , searchHost[i])){
          http_value->is_search = 1;
          break;
        }
        if(NULL == searchHost[i+1]){
          http_value->is_search = -1;
          // 只对host进行一次匹配
          return;
        }
    }
  }
  if(http_value->is_search == 2){
    // 已经找到
    // 暂时只进行一次赋值，如有要求再取消该条件
    return;
  }
  for(int i =0; searchKey[i]!= NULL; i++){
    char*str = strstr(uri_key, searchKey[i]);
    if (str!= NULL) {
      size_t val_len = 0;
      if(NULL != p_next){
        val_len = p_next - p_start - copy_len;
      }else {
        val_len = strlen(p_start) - copy_len;
      }
      val_len = val_len > (sizeof(http_value->uriSearch) - strlen(http_value->uriSearch))
                    ? (sizeof(http_value->uriSearch) - strlen(http_value->uriSearch) - 1)
                    : val_len;
      if(0 == strlen(http_value->uriSearch )){
        memcpy(http_value->uriSearch , p_start + copy_len , val_len);
      }else {
        strcat(http_value->uriSearch ,",");
        strncat(http_value->uriSearch, p_start+copy_len, val_len);
        http_value->is_search = 2;
      }
    }
  }
}


void dissect_uri_keys(void* user,char* key,int key_len,char* value,int val_len){
  USER * user_ = user;

  struct http_info *http_value = user_->http_value;
  struct http_session *session = user_->session;
  geturiSearch_v2(http_value,session,http_value->is_request,key,key_len,value,val_len);
  if(session->uri_keys_num >= URI_KEY_MAX){
    return;
  }
  memcpy(session->uri_keys[session->uri_keys_num],key,key_len);
  session->uri_keys_num ++;
}

static void get_uri_keys_v2(struct http_info *http_value,struct http_session * session,uint8_t is_http_request){
      //  获取uri key
    // ex: /WebResource.axd?d=pynGkmcFUV13He1Qd6_TZLRDLpL6gmauvyvofGdAzgtAYfxCeGItmqtnuL8g29HqNiBfEA2&t=635802961220000000
    memset(session->uri_keys, 0, sizeof(session->uri_keys));
    session->uri_keys_num = 0;

    char *p_param = memchr(session->uri, '?', strlen(session->uri));

    if (!p_param)
        return;

    int i = 0;
    char *p_start = p_param + 1;
  USER user;
  user.session = session;
  user.http_value = http_value;
  get_parameters_keys_values(p_start,strlen(p_start),&user,dissect_uri_keys);
}

static void get_uri_keys(struct http_info *http_value,struct http_session * session,uint8_t is_http_request) {
    //  获取uri key
    // ex: /WebResource.axd?d=pynGkmcFUV13He1Qd6_TZLRDLpL6gmauvyvofGdAzgtAYfxCeGItmqtnuL8g29HqNiBfEA2&t=635802961220000000
    memset(session->uri_keys, 0, sizeof(session->uri_keys));
    session->uri_keys_num = 0;

    char *p_param = memchr(session->uri, '?', strlen(session->uri));

    if (!p_param)
        return;

    int i = 0;
    char *p_start = p_param + 1;
    char *p_eq;
    //        int len = copy_len - (p_start - session->uri);
  // p_start 始终为&后的一个字节
  while (p_start) {
    char    *p_next = memchr(p_start, '&', strlen(p_start));
    uint64_t copy_len = 0;
    if (p_next) {
        p_eq = memchr(p_start, '=', p_next - p_start);
        copy_len = p_eq - p_start;
    } else {
        //最后一个key
        p_eq = memchr(p_start, '=', strlen(p_start));
        copy_len = p_eq - p_start;
    }
    if (copy_len > 0) {
        memcpy(session->uri_keys[i++], p_start,
            copy_len > sizeof(session->uri_keys[0]) ? sizeof(session->uri_keys[0]) - 1 : copy_len);
        geturiSearch(http_value,session,is_http_request,session->uri_keys[i-1],p_start,p_next,copy_len);
    }

    if (p_next) {
        p_start = p_next + 1;
    } else {
        break;
    }

    // 最多装载50个key
    if (i >= URI_KEY_MAX)
            break;
  }

    session->uri_keys_num = i;
}

static void get_via_num(struct http_info *http_value) {

    http_value->via_num = 0;

    struct header_value *value = (struct header_value *)g_hash_table_lookup(http_value->table, "via");
    if (value)
    {
        char tmp[2048];

        int len = DPI_MIN(sizeof(tmp)-1, value->len);
        memset(tmp, 0, sizeof(tmp));
        strncpy(tmp, (const char *)value->ptr, len);

        char *end = NULL;
        const char *token = ",";
        char *p_next = NULL;

        p_next = strtok(tmp, token);
        while (p_next) {

            http_value->via_num++;
            if (p_next[0] == '\0')
                break;

            p_next = strtok(NULL, token);
        }
    }
}

const char *strrchr_until(const char *pBegin, char key, char untilKey) {
  const char *pLastKey = NULL;
  for (const char *p = pBegin; *p && *p != untilKey; p++) {
    if (*p == key) {
      pLastKey = p;
    }
  }

  return pLastKey ? pLastKey : NULL;
}


#define MD5_DIGEST_LENGTH 16
static int http_get_method_file_suffix(const char *uri, char *child_dir, char *file_raw_name, char *suffix) {
  if (uri == NULL || strlen(uri) == 0 || suffix == NULL) {
    return -1;
  }
  int   i = 0;
  int   j = 0;
  char *p = NULL;
  int   check_len = 0;
  char  http_getname[COMMON_FILE_NAME] = {0};
  int   http_getname_len = 0;
  int   len = 0;

  p = strchr(uri, '?');
  if (p != NULL) {
    check_len = p - uri;
  } else {
    check_len = strlen(uri);
  }

  char md5sum[COMMON_FILE_NAME] = {0};

  for (i = check_len - 1; i >= 0; i--) {
    if (uri[i] == '/') {
      if (i + 1 > COMMON_FILE_NAME) {
        return -1;
      } else {
        strncpy(child_dir, uri, i + 1);
      }

      len = check_len - i - 1;
      if (len >= COMMON_FILE_NAME)
        len = COMMON_FILE_NAME - 1;
      strncpy(http_getname, &uri[i + 1], len);
      http_getname_len = len;
      break;
    }
  }
  if (0 == http_getname_len) {
    return -1;
  }

  len = 0;
  // 获取需要计算md5的字符串
  // char path_compo[1024] = {0};
  const gchar *path_compo = strrchr_until(uri, '/', '?');
  size_t       len_path_compo = strlen(path_compo);
  if (path_compo[len_path_compo - 1] == '/') {
    static const gchar INDEX[] = "index";
    path_compo = INDEX;
  }
  MD5((const uint8_t *)path_compo, strlen(path_compo), (uint8_t *)md5sum);

  int start = 0;
  int end = 0;

  uint8_t flag = 0;
  for (i = http_getname_len - 1; i >= 0; i--) {
    if (http_getname[i] == '.') {
      start = i;
      flag = 1;
      strncpy(file_raw_name, http_getname, (i >= COMMON_FILE_NAME) ? COMMON_FILE_NAME - 1 : i);
      break;
    }
  }

  if (1 == flag) {
    for (j = start + 1; j < (int)strlen(http_getname); j++) {
      if (!isalpha(http_getname[j])) {
        if (!isdigit(http_getname[j])) {
          end = j;
          break;
        } else {
          if (j == (int)strlen(http_getname) - 1) {
            end = j + 1;
          }
          continue;
        }
      }
      end = j + 1;
    }
  } else {
    snprintf(file_raw_name, COMMON_FILE_NAME, "%s", http_getname);
  }

  len = end - start;
  if (start > 0 && len > 0) {
    if (len > COMMON_FILE_NAME)
      len = COMMON_FILE_NAME;
    strncpy(suffix, &http_getname[start], len);
  } else {
    return -1;
  }

  return 1;
}

static void gen_content_file_name(struct flow_info *flow, struct http_info *http_value, char *filename, int filename_len)
{
    if(!flow || !flow->app_session)
        return;
    if(!http_value)
        return;
    if(!filename || filename_len<=0)
        return;
    if(http_value->PROTOCOL_VAL_LEN(content) <= 0)
        return;

    char    suffix[COMMON_FILE_NAME]={0};
    char    child_dir[COMMON_FILE_PATH]={0};
    char    file_writing_dir[COMMON_FILE_PATH]={0};
    char    file_raw_name[COMMON_FILE_NAME]={0};
    int     ret;
    struct http_session *session = (struct http_session *)flow->app_session;
    //使用默认后缀，避免依据特征修改后缀有问题；
    snprintf(suffix,sizeof(suffix),".bin");
    if(session->uri){
        ret=http_get_method_file_suffix(session->uri, child_dir,file_raw_name, suffix);
        if(ret<0){
            return;
        }
     }
     bzero(filename, filename_len);
     snprintf(file_writing_dir, COMMON_FILE_PATH, "%s/%s/",
                                 g_config.tbl_out_dir, tbl_log_array[TBL_LOG_HTTP].protoname);
     if (access(file_writing_dir, F_OK))
         mkdirs(file_writing_dir);

     struct timeval tv;
     gettimeofday(&tv, NULL);
     snprintf(filename, filename_len, "%s/http_%s_%06ld_%03d", file_writing_dir, time_to_datetime(g_config.g_now_time), tv.tv_usec, flow->thread_id);
     if (g_config.show_task_id) {
         strcat(filename, "_");
         strcat(filename, g_config.task_id);
     }
     strcat(filename, suffix);
}

static void http_session_head_reset(struct flow_info *flow,uint8_t direction ) {
  struct http_session      *session = flow->app_session;

  session->write_offset = 0;
  session->deal_content_len = 0;
  session->http_chunked_len = 0;
  session->http_chunked_write_len = 0;
  session->is_valid = 0;
  session->is_boundary = 0;
  session->is_formdata = 0;
  session->is_range = 0;
  session->body_state = HTTP_BODY_TYPE_UNKNOWN;
  session->file = NULL;
  session->filter = 0;
  session->form_urlencoded = 0;

  if (session->record) {
    write_http_record_log(flow,session->record);
    session->record = NULL;

  }

}
static int
dissect_http_head(struct flow_info *flow, uint8_t* is_req, const uint8_t *payload, const int32_t payload_len) {
    struct http_session      *session = flow->app_session;
    struct http_info *http_value = &session->http_value;
    int                       offset = 0;
    gpointer                  _value;
    struct header_value      *value;
    //重置状态
    http_session_head_reset(flow,*is_req);
    offset = parse_http_line_info(payload, payload_len, http_value);
    if (!http_value->table) {
      goto exit;
    }
    _value = g_hash_table_lookup(http_value->table, "head_line");
    if (!_value) {
      goto exit;
    }
    value = (struct header_value *)_value;
    if (0 == memcmp(payload, "HTTP/1.", 7)) {
      *is_req = 0;
      http_value->is_request = 0;
    } else {
      *is_req = 1;
      http_value->is_request = 1;
    }
    if (*is_req) {
      uint32_t uri_start = 0;
      session->direction[FLOW_DIR_SRC2DST] += 1;

      for (int i = 0; HTTP_METHOD[i].method; i++) {
        if (0 == strncasecmp((const char *)payload, HTTP_METHOD[i].method, HTTP_METHOD[i].len)) {
          uri_start = HTTP_METHOD[i].len;
          session->request_count++;
          break;
        }
      }
      if (value->len > (9 + uri_start)) {
        http_value->PROTOCOL_VAL_PTR(method) = value->ptr;
        http_value->PROTOCOL_VAL_LEN(method) = uri_start - 1;

        http_value->PROTOCOL_VAL_PTR(uri) = &value->ptr[uri_start];
        http_value->PROTOCOL_VAL_LEN(uri) = value->len - (uri_start + 9);

        http_value->PROTOCOL_VAL_PTR(version) = http_value->PROTOCOL_VAL_PTR(uri) + http_value->PROTOCOL_VAL_LEN(uri) + 1;
        http_value->PROTOCOL_VAL_LEN(version) = 8;

        http_value->req_version_num++;

        if (memcmp(http_value->PROTOCOL_VAL_PTR(version), "HTTP/1.", 7)) {
          session->direction[FLOW_DIR_SRC2DST] -= 1;
          goto exit;
        }

        get_http_method(http_value, session); /* 获取http请求method，回填到响应tbl中 */
        get_http_host(http_value, session);
        if(g_config.http.http_exquisite_switch){
          get_http_uri(http_value, session);    /* 获取http请求uri，回填到响应tbl中 */
          get_uri_keys_v2(http_value,session,*is_req); // 获取http uri中的key的数据和数量
          if (strlen(session->uri) > 0 && strlen(session->host) > 0) {
            snprintf(http_value->url, sizeof(http_value->url), "http://%s%s", session->host, session->uri);
          }
          dissect_http_user_agent(http_value); /*在还原程序中暂不需要此函数进行处理*/
          dissect_http_authorization(http_value,session);
          get_http_username_password(http_value, session);
        }
      }
    } else {
      session->response_count++;
      session->direction[FLOW_DIR_DST2SRC] += 1;
      dissect_http_authenticate(http_value);

      if (value->len > 12) {
        http_value->PROTOCOL_VAL_PTR(version) = value->ptr;
        http_value->PROTOCOL_VAL_LEN(version) = strlen("HTTP/1.1");

        http_value->PROTOCOL_VAL_PTR(code) = &value->ptr[strlen("HTTP/1.1 ")];
        http_value->PROTOCOL_VAL_LEN(code) = 3;
        //add by huangzw 20220920
        if (strlen(session->username) && memcmp(http_value->PROTOCOL_VAL_PTR(code), "200", 3) == 0) {
          session->is_valid = 1;
        } else {
          session->is_valid = 0;
        }

        if (memcmp(http_value->PROTOCOL_VAL_PTR(code), "404", 3) == 0) {
          session->get_err = 1;
        }

        http_value->PROTOCOL_VAL_PTR(response_code) = &value->ptr[strlen("HTTP/1.1 200 ")];
        http_value->PROTOCOL_VAL_LEN(response_code) = value->len - strlen("HTTP/1.1 200 ");
      }
      //HTTP response status
      if(g_config.http_status_whitelist[0] != '*'){
          char status[4];
          memcpy(status, http_value->PROTOCOL_VAL_PTR(code), 3);
          status[3] = 0;
          if(!strstr(g_config.http_status_whitelist, status))
              goto exit;
      }
    }
    g_hash_table_remove(http_value->table, "head_line");

    //查看头部行的transfer-encoding是否是chunked
    _value = g_hash_table_lookup(http_value->table, "transfer-encoding");
    if (_value) {
      char transfer[64] = {0};
      value = (struct header_value *)_value;
      memcpy(transfer, value->ptr, value->len >= sizeof(transfer) ? sizeof(transfer) - 1 : value->len);
      if (strcasestr(transfer, "chunked")) {
        http_session_chunked_reset(session);
      }
    }
    _value = g_hash_table_lookup(http_value->table, "content-encoding");
    if (_value) {
      value = (struct header_value *)_value;
      if (strcmp("gzip", (char *)value->ptr))
        http_value->encoding = 1;
    }
    if(g_config.http.http_exquisite_switch){
        get_via_num(http_value);    // 获取via个数
    }
    char c_type[512] = {0};
    _value = g_hash_table_lookup(http_value->table, "content-type");
    if (_value) {
        value = (struct header_value *)_value;
        memcpy(c_type, value->ptr, value->len >= sizeof(c_type) ? sizeof(c_type) - 1 : value->len);
        #if 0
        if (strcasestr(c_type, "application/ocsp-request")) {
          if (strstr(flow->proto_info, "OCSP") == NULL) {
            strcat(flow->proto_info, ".OCSP");
          }
          dissect_ocsp_request(flow, payload, payload_len);
        } else if (strcasestr(c_type, "application/ocsp-response")) {
          if (strstr(flow->proto_info, "OCSP") == NULL) {
            strcat(flow->proto_info, ".OCSP");
          }
          dissect_ocsp_response(flow, payload, payload_len);
        }
        #endif
        if (strcasestr(c_type, "boundary")) {
          // Content-Type: multipart/form-data; boundary=---------------------------************
          int   boundary_len = 0;
          char *boundary_ptr = NULL;
          get_data_key_value(c_type, strlen(c_type), "boundary=", 0, &boundary_ptr, &boundary_len);
          if (0 != boundary_len && NULL != boundary_ptr) {
            set_boundary_key_info(http_value, boundary_ptr, boundary_len);
            session->is_boundary = 1;
          }
        }else if (strcasestr(c_type, "application/x-www-form-urlencoded")) {
            session->form_urlencoded = 1;
        } else if ((!strcmp(c_type, "image") || !strcmp(c_type, "video"))) {
            session->rsp_fulltext_len = 1;
        }

        if (0 == *is_req && 0 == _http_content_type_filter(c_type)) { /* 只对S2C生效 */
            goto exit;

        }
        memset(session->c_type, 0, sizeof(session->c_type));
        strncpy(session->c_type, c_type, strlen(c_type) > sizeof(session->c_type) ? sizeof(session->c_type) - 1 : strlen(c_type));
    } else if (0 == *is_req && 1 == g_config.http.drop_no_content_type) { /* no content-type丢弃与否也只对S2C生效 */
      goto exit;
    }
    //获取实际的内容长度

    _value = g_hash_table_lookup(http_value->table, "content-length");
    if (_value) {
      value = (struct header_value *)_value;
      char buff[16];
      memcpy(buff, value->ptr, value->len > sizeof(buff) ? sizeof(buff) - 1 : value->len);
      session->body_state = HTTP_BODY_TYPE_CONTENT_LENGTH;
      session->content_len = atoi(buff);
    }

    _value = (struct header_value *)g_hash_table_lookup(http_value->table, "cookie");
      if(_value)
      {
          value = (struct header_value *)_value;
          const uint8_t *tmp = memchr(value->ptr, '=', value->len);
          if (tmp) {
              int len = tmp - value->ptr;
              char webmail_type[TYPE_LEN];
              memset(webmail_type, 0, sizeof(webmail_type));
              memcpy(webmail_type, value->ptr, DPI_MIN(len, TYPE_LEN - 1));
              if (strncasecmp(webmail_type, "JSESSIONID", 10) == 0) {
                  int status = 0;
                  char content_ptr[1024 * 1024] = {0};

                  status = YV_HttpPostParse_ParseRAW(
                      NULL, (const char *)http_value->PROTOCOL_VAL_PTR(content),
                      http_value->PROTOCOL_VAL_LEN(content), (char *)content_ptr,
                      1024 * 1024);
                  len = dpi_url_decode(content_ptr, strlen(content_ptr),
                                      content_ptr, strlen(content_ptr));
                  content_ptr[len] = '\0';
                  // if (is_http_request) {
                  //     dissect_webmail(flow, 0, (const uint8_t *)content_ptr + 4,
                  //                     len - 4);
                  // } else {
                  //     dissect_webmail(flow, 1, (const uint8_t *)content_ptr + 4,
                  //                     len - 4);
                  // }
              }
          }
      }

    //处理分片传输
    _value = (struct header_value *)g_hash_table_lookup(http_value->table, "range");
    if (_value) {
      dissect_http_range(flow, _value);
    }

    _value = (struct header_value *)g_hash_table_lookup(http_value->table, "content-range");
    if (_value) {
      dissect_http_content_range(flow, _value);
    }

    _value = (struct header_value *)g_hash_table_lookup(http_value->table, "content-disposition");
    if (_value) {
      value = (struct header_value *)_value;
      dissect_http_content_disposition(http_value, (const char *)value->ptr, value->len);
    }
    if (!g_config._327_common_switch) {

        high_app_proto_http_identify(flow, http_value);
    }

  exit:

    if (!session->record) {
        session->record = http_creat_record(flow, FLOW_DIR_SRC2DST, http_value);
    }

    g_hash_table_destroy(http_value->table);
    http_value->table = NULL;
    return offset;
}

static int has_chunked(const char*p, int l)
{
    const char *find_str[] = {"\x0D\x0A\x0D\x0A", "chunked", "Transfer-Encoding", "HTTP/1.", NULL};
    const char *find       = p + l;
    int         i;

    for(i = 0; NULL != find_str[i]; i++)
    {
        find = memmem(p, find -p, find_str[i],  strlen(find_str[i]));
        if(NULL == find)
        {
            return 0;
        }
    }
    return 1;
}

static int64_t get_chunkedlen(const char *http, int http_len)
{
    int64_t      offset      = 0;
    int64_t      chunk_size  = 0;
    int64_t      chunk_len   = 0;
    const char   *str_split   = "\x0D\x0A\x0D\x0A";
    const char   *p           = NULL;

    if(0 == has_chunked(http, http_len))
    {
        return -1;  // no chunk
    }

    // skip http head
    p = memmem(http, http_len, str_split,  strlen(str_split));
    if(NULL == p)
    {
        return 0;
    }

    offset    = p - http + strlen(str_split);
    http     += offset;
    http_len -= offset;
    offset    = 0;

    while (offset < http_len)
    {
        chunk_len = find_packet_line_end((const uint8_t*)http + offset, (uint32_t)http_len - offset);
        if (chunk_len <= 0)
        {
            break;
        }

        chunk_size = strtol(http + offset, NULL, 16);
        if(chunk_size > (1024*1024*10) || chunk_size < 0) // 限定每个chunk_size
        {
            return -1;  // no chunk
        }

        offset    += (chunk_len + 2 + chunk_size + 2);  // point next chunked
        if (offset  > http_len)
        {
            break;
        }
        else
        if (0 == chunk_size && offset == http_len)
        {
            return http_len;
        }
    }
    return HTTP_NEED_MORE;
}

// 返回 Content-Length 数值类型
static int64_t get_contentlen(const char *p, int l)
{
#define CONTENT_STR_LEN 16
    const char *str_start   = "\r\nContent-Length";
    const char *str_end     = "\r\n";

    if(l < CONTENT_STR_LEN)
        return 0;

    const char *find_start = memmem(p, l, str_start,  CONTENT_STR_LEN);
    if(find_start)
        find_start += CONTENT_STR_LEN;
    else
        return 0;

    const char *find_end = memmem(find_start, l-(find_start-p), str_end,  strlen(str_end));
    if(find_end == NULL || find_start + 15 < find_end)  //0xffffffff = 4294967295 = 4G
        return 0;

    int  i = 0;
    char buff[16];
    while(find_start < find_end)
    {
        if(isdigit(*find_start))
            buff[i++] =  *find_start;
        else if(*find_start != ':' && *find_start != ' ')
            return 0;
        find_start++;
    }
    buff[i] = 0;

    return atol(buff);
}

/*
*  0: not http
*  1: is  http
*/
int is_http(const char *p, int l) {
  int ST;
  int offset;
  int i;
  int find;

  enum {
    HTTP_ST_DEFAULT,
    HTTP_ST_REQUEST,
    HTTP_ST_REQUEST_METHOD,
    HTTP_ST_REQUEST_URI,
    HTTP_ST_REQUEST_VERSION,
    HTTP_ST_RESPONSE,
    HTTP_ST_RESPONSE_VERSION,
    HTTP_ST_RESPONSE_NUM,
    HTTP_ST_END,
  };

  if (l <= 0) {
    return 0;
  }

  ST = HTTP_ST_DEFAULT;
  offset = 0;
  while (offset < l) {
    switch (ST) {
      case HTTP_ST_DEFAULT:
        if (0 == memcmp(p, "HTTP/1.", 7)) {
          ST = HTTP_ST_RESPONSE;
        } else {
          ST = HTTP_ST_REQUEST;
        }
        break;

      case HTTP_ST_REQUEST:
        ST = HTTP_ST_REQUEST_METHOD;
        break;

      case HTTP_ST_REQUEST_METHOD:
        find = 0;
        if (l - offset > 10) {
          for (i = 0; HTTP_METHOD[i].method; i++) {
            if (0 == strncasecmp(p + offset, HTTP_METHOD[i].method, HTTP_METHOD[i].len)) {
              offset += HTTP_METHOD[i].len;
              ST = HTTP_ST_END;
              find = 1;
              break;
            }
          }
        }
        if (0 == find) {
          return 0;
        }
        break;

      case HTTP_ST_REQUEST_VERSION:
        if (0 == memcmp(p + offset, "HTTP/1.", strlen("HTTP/1."))) {
          offset += strlen("HTTP/1.");
          ST = HTTP_ST_END;
        } else {
          return 0;
        }
        break;

      case HTTP_ST_RESPONSE:
        ST = HTTP_ST_RESPONSE_VERSION;
        break;

      case HTTP_ST_RESPONSE_VERSION:
        if (0 == memcmp(p + offset, "HTTP/1.", strlen("HTTP/1."))) {
          offset += strlen("HTTP/1.");
          offset++;
          offset++;
          ST = HTTP_ST_RESPONSE_NUM;
        } else {
          return 0;
        }
        break;

      case HTTP_ST_RESPONSE_NUM:
        for (i = 0; i < 3; i++) {
          if (0 == isdigit(p[offset + i])) {
            return 0;
          }
        }
        ST = HTTP_ST_END;
        break;

      case HTTP_ST_END:
        return 1;
        break;
    }
  }
  return 0;
}



// 返回 HTTP head 长度范围
// 负数: 没有找到
static int64_t get_header_len(const char *p, int l)
{
    if(0 == is_http(p,l))
    {
        return HTTP_ERROR;
    }

    const char *find = memmem(p, l, "\x0D\x0A\x0D\x0A", 4);
    if(NULL == find)
    {
        return HTTP_NEED_MORE;
    }

    return  find - p + 4;
}


// 返回负数, 代表不存在一个完整HTTP 长度
// 返回正数, 代表  存在一个完整HTTP 长度
static int64_t get_http_len(const char *p, int len)
{
    const uint8_t *pstart   = NULL;
    const uint8_t *pend     = NULL;
    const uint8_t *data     = NULL;
    int64_t        remain   = 0;
    int64_t        success  = 0;
    int64_t        hl       = 0;  // header  len
    int64_t        cl       = 0;  // content len

    if(len < 0)
    {
        return -1;
    }

    hl = get_header_len(p, len);
    if(HTTP_NEED_MORE == hl)
    {
        return HTTP_NEED_MORE;
    }
    else if(HTTP_ERROR == hl)
    {
        return HTTP_ERROR;
    }

    cl = get_contentlen(p, hl);
    if(cl > 0)
    {
        return hl + cl;
    }

    cl = get_chunkedlen(p, len);
    if(cl > 0)
    {
        return hl + cl;
    }
    else if(cl == HTTP_NEED_MORE)
    {
        return HTTP_NEED_MORE;
    }

    return hl;
}

// 已缓存的数据直接输出
static int http_tcp_over(struct flow_info *flow)
{
    // 重组结束
    return 0;
}






//构造ip+uri作为查表的key值
static char *http_url_name_key_create(struct flow_info *flow, struct http_info *http_value, uint8_t is_req) {
  char                *key;
  struct http_session *session = (struct http_session *)flow->app_session;
  char                 ip_str[64] = {0};
  int                  malloc_len;

  if (flow->tuple.inner.port_dst > flow->tuple.inner.port_src) {
    get_ipstring(flow->tuple.inner.ip_version, flow->tuple.inner.ip_dst, ip_str, sizeof(ip_str));
  } else {
    get_ipstring(flow->tuple.inner.ip_version, flow->tuple.inner.ip_src, ip_str, sizeof(ip_str));
  }
  malloc_len = strlen(ip_str) + strlen(session->host) + 1 + 3;
  if (strlen(session->decode_uri_filename)) {
    malloc_len += strlen(session->decode_uri_filename) + 1;
    key = malloc(malloc_len);
    memset(key, 0, malloc_len);
    strcat(key, ip_str);
    strcat(key, session->decode_uri_filename);
    strcat(key, session->host);
  } else {
    malloc_len += strlen(http_value->http_filename) + 1;
    key = malloc(malloc_len);
    memset(key, 0, malloc_len);
    strcat(key, ip_str);
    strcat(key, http_value->http_filename);
    strcat(key, session->host);
  }
  if (is_req) {
    strcat(key, "req");
  } else {
    strcat(key, "rsp");
  }
  key[malloc_len - 1] = '\0';
  return key;
}

static struct http_file *http_file_async_write(struct flow_info *flow, uint8_t is_req, uint8_t *payload, int payload_len) {
  struct http_session      *session = (struct http_session *)flow->app_session;
  struct http_info *http_value = &session->http_value;
  struct http_file         *file_st = NULL;
  char                     *key = http_url_name_key_create(flow, http_value, is_req);

  file_st = malloc(sizeof(struct http_file));
  memset(file_st, 0, sizeof(struct http_file));
  if (session->decode_uri_filename[0] != '\0') {
    strncpy(file_st->name, session->decode_uri_filename,
        strlen(session->decode_uri_filename) > sizeof(file_st->name) - 1 ? sizeof(file_st->name) - 1
                                                                            : strlen(session->decode_uri_filename));
  } else {
    strncpy(file_st->name, http_value->http_filename,
        strlen(http_value->http_filename) > sizeof(file_st->name) - 1 ? sizeof(file_st->name) - 1
                                                                        : strlen(http_value->http_filename));
  }
  strncpy(file_st->local, http_value->filename, strlen(http_value->filename));
  if (strlen(file_st->name) > 1 && file_st->name[strlen(file_st->name) - 1] == '.') {
    file_st->name[strlen(file_st->name) - 1] = '\0';
  }
  strcpy(file_st->c_type, session->c_type);

  if (payload && payload_len > 0) {
    file_st->frame_body = malloc(payload_len + 1);
    if (file_st->frame_body) {
      memcpy(file_st->frame_body, (const void *)payload, payload_len);
      file_st->frame_len = payload_len;
    }
  }

  if (session->record) {
    file_st->record =session->record;
    // precord_destroy(session->record);
    session->record = NULL;
  }

  file_st->has_range = session->range.has_range;
  file_st->encoding = http_value->encoding;
  file_st->write_offset = session->write_offset;
  file_st->flow = dpi_flow_clone(flow);

  dpi_conv_insert_key_value(flow, key, strlen(key), (uint8_t *)file_st, sizeof(struct http_file), CONV_DO_INSERT);

  free(key);

  return file_st;
}

static int filename_writing_offload(struct http_file *file) {
  char newname[1024];
  snprintf(newname, sizeof(newname), "%s", file->local);
  char *find = my_strrstr(newname, ".writing");
  {
    *find = '\0';
    rename(file->local, newname);
    snprintf(file->local, sizeof(file->local), "%s", newname);
  }
  return 0;
}

static int file_type_detect(struct http_file *file) {
  char newname[1024] = {0};
  snprintf(newname, sizeof(newname), "%s", file->local);

  discern_filetype((const uint8_t *)file->head, sizeof(file->head), newname, sizeof(newname));
  if (strcmp(newname, file->local)) {
    rename(file->local, newname);
    snprintf(file->local, sizeof(file->local), "%s", newname);  //update
  }
  return 0;
}

static int file_set_status(struct http_file *file, const char *status) {
  char filelocal[1024] = {0};
  snprintf(filelocal, sizeof(filelocal), "%s", file->local);

  //计算 文件目录, 文件名
  const char *filedir = filelocal;
  char       *filebase = basename(filelocal);
  if (filebase) {
    *(filebase - 1) = '\0';
  }

  char newname[1024] = {0};
  snprintf(newname, sizeof(newname), "%s/%s_%s", filedir, status, filebase);

  //update
  rename(file->local, newname);
  snprintf(file->local, sizeof(file->local), "%s", newname);  //update
  return 0;
}

static int http_file_close(struct http_file *file, int close_immediately) {
  char        buff[1024];
  const char *str_err = "";
  // uint64_t    file_len = file_rsm_len(file->file_rsm);
  // uint64_t    file_write = file_rsm_write(file->file_rsm);
  int status = 0;
  //文件没有存够 的得等到超时
  // if (0 == close_immediately && file->eof != file_len) {
  //   return file->eof == file_len;
  // }
  fseek(file->fp, 0, SEEK_END);
  size_t file_size = ftell(file->fp);
  if (file->encoding) {
    freopen(file->local, "r", file->fp);
    char *inbuffer = (char *)malloc(file_size);
    if (inbuffer) {
      int rl = fread(inbuffer, 1, file_size, file->fp);
      rewind(file->fp);
      char outbuffer[1024 * 1024] = {0};
      status = YV_HttpPostParse_ParseRAW(NULL, (const char *)inbuffer, file_size, (char *)outbuffer, sizeof(outbuffer));
      if (status > 0) {
        freopen(file->local, "wb", file->fp);
        fwrite(outbuffer, 1, status, file->fp);
        memcpy(file->head, outbuffer, strlen(outbuffer) < sizeof(file->head) ? strlen(outbuffer) : sizeof(file->head) - 1);
      }
      free(inbuffer);
    }
  }
  fclose(file->fp);
  file->fp = NULL;

  filename_writing_offload(file);
  if (status > 0 || strlen(file->suffix) > 10) {
    // 重置后缀
    for (int i = 0; HTTP_CTYPE[i].c_type; i++) {
      if ((int)strlen(file->c_type) >= HTTP_METHOD[i].len) {
        if (0 == strncasecmp((const char *)file->c_type, HTTP_CTYPE[i].c_type, HTTP_METHOD[i].len)) {
          snprintf(file->suffix, sizeof(file->suffix), "%s", file->c_type + HTTP_CTYPE[i].len);
          for (unsigned long i = 0; i < strlen(file->suffix); i++) {
            if (!isalpha(file->suffix[i])) {
              file->suffix[i] = '\0';
              break;
            }
          }
          break;
        }
      }
    }
    char newname[1024];
    snprintf(newname, sizeof(newname), "%s", file->local);
    char *find = my_strrstr(newname, ".");
    {
      *find = '\0';
      strcat(newname, ".");
      strcat(newname, file->suffix);
      rename(file->local, newname);
      snprintf(file->local, sizeof(file->local), "%s", newname);
    }
  }
  //探测格式
  if ('\0' == file->suffix[0] || strcmp(file->suffix, "bin") == 0) {
    file_type_detect(file);
  }


  char *local = basename(file->local);

  return 1;
}

static FILE *http_open_file(struct http_file *file) {
  char  localname[COMMON_FILE_PATH];
  char *file_suffix = NULL;
  char *file_suffix1 = NULL;
  char *file_suffix2 = NULL;

  //像这样的文件路径,没有后缀,但是存在 "."
  //-rw-r--r--. 1 <USER> <GROUP> 41K Aug  2  2017 /usr/include/c++/4.8.2/tr2/dynamic_bitset
  //-rw-r--r--. 1 <USER> <GROUP> 1.4K Aug 2  2017 \usr\include\c++\4.8.2\parallel\algorithm
  //-rw-r--r--. 1 <USER> <GROUP> 1.4K Aug 2  2017 APP 动作抓包流程介绍(1).docx
  //如果文件名中存在 "/" 或 "\", 那就继续搜索后缀
  file_suffix1 = (char *)g_strrstr_len(file->name, strlen(file->name), "/");
  file_suffix2 = (char *)g_strrstr_len(file->name, strlen(file->name), "\\");
  file_suffix = file_suffix1 ? file_suffix1 : file_suffix;
  file_suffix = file_suffix2 ? file_suffix2 : file_suffix;
  if (file_suffix) {
    file_suffix++;
    file_suffix = (char *)g_strrstr_len(file_suffix, strlen(file_suffix), ".");
  } else {
    file_suffix = (char *)g_strrstr_len(file->name, strlen(file->name), ".");
  }
  const char *suffix = file_suffix ? file_suffix + 1 : "bin";
  if (suffix[0] == '.') {
    suffix++;
  }

  get_special_filename(NULL, "http", suffix, localname, sizeof(localname), 1);
  // strcat(localname, file->name);
  strcat(localname, ".writing");
  snprintf(file->local, sizeof(file->local), "%s", localname);
  snprintf(file->suffix, sizeof(file->suffix), "%s", suffix);

  // printf("[file open ] %s \n", file->name);
  FILE *f = fopen(file->local, "wb");
  if (NULL == f) {
    perror(file->local);
    return NULL;
  }
  file->fp = f;

  // file->file_rsm = file_rsm_init(64);  //SMB2的offset长度是8字节

  return f;
}

static int http_parser_boundary_start_len(
    struct http_session *session, const uint8_t *payload, int payload_len, uint64_t *content_len) {
  // 以--$(boundary_key)为开头
  struct http_info *http_value = &session->http_value;
  *content_len = 0;
  uint32_t offset = 0;
  uint8_t *boundary_p = (uint8_t *)payload;
  int      total_len = payload_len;
  int      Content_Disposition_len = 0;
  uint8_t *boundary_file_start = NULL;
  uint8_t *boundary_file_end = NULL;
  int      remaining = payload_len;
  uint8_t  ret = 0;
  while (remaining) {
    // 以--$(boundary_key)为开头
    if (!boundary_p) {
      //上一次处理是最后一行
      break;
    }
    // 找 boundary_key 后的第一个双换行符
    Content_Disposition_len = _find_empty_line(boundary_p, remaining);
    if (Content_Disposition_len >= 0) {
      ret = http_parser_content_disposition_header(http_value, boundary_p, Content_Disposition_len);
      Content_Disposition_len += 2;
      //每次更新file_start 当处理到最后一个content_disposition时 为真实的 file_start
      boundary_file_start = boundary_p + Content_Disposition_len;
    }
    if (ret) {
      session->boundary_state = HTTP_BOUNDARY_STATE_WRITING;
      *content_len = payload_len - (boundary_file_start - payload);
      break;
    }
    remaining -= Content_Disposition_len;
    boundary_p = find_next_boundary(http_value, boundary_file_start, remaining);
    if (boundary_p && boundary_file_start) {
      remaining -= (boundary_p - boundary_file_start);
    }
  }
  if (boundary_file_start) {
    return boundary_file_start - payload;
  }
  return 0;
}

static int http_parser_boundary_end_len(struct http_info *http_value, const uint8_t *payload, int payload_len) {
  uint8_t *boundary_p = find_next_boundary(http_value, payload, payload_len);
  if (boundary_p) {
    return payload_len - (boundary_p - payload);
  }
  return 0;
}

/*还原boundary实体文件*/
static uint8_t *http_boundary_file_find_start(
    struct http_session *session, const uint8_t *payload, int payload_len, uint64_t *file_len, uint64_t *el) {
  struct http_info *http_value = &session->http_value;
  int                       offset = 0;
  uint64_t                  file_data_size = 0;
  uint8_t                  *file_data_start = NULL;

  switch (session->boundary_state) {
    case HTTP_BOUNDARY_STATE_UNKNOWN:
      offset = http_parser_boundary_start_len(session, (uint8_t *)payload, payload_len, &file_data_size);
      if (http_value->is_request == 1) {
        precord_put_to_layer(session->record, "http", "conDispUp", string, http_value->conDisp);
      } else {
        precord_put_to_layer(session->record, "http", "conDispDown", string, http_value->conDisp);
      }
      file_data_start = (uint8_t *)payload + offset;
      *file_len = file_data_size;
      break;
    case HTTP_BOUNDARY_STATE_WRITING:
      file_data_start = (uint8_t *)payload;
      int boundary_end_len = http_parser_boundary_end_len(http_value, (uint8_t *)payload, payload_len);
      *file_len = payload_len - boundary_end_len;
      *el = boundary_end_len;
      if (boundary_end_len) {
        session->boundary_state = HTTP_BOUNDARY_STATE_UNKNOWN;
        session->is_boundary = 0;
      }
      break;
  }
  return (uint8_t *)file_data_start;
}

static uint8_t *http_dissect_raw_file_name(struct http_session *session, uint8_t is_request, uint8_t *payload,
    uint64_t *payload_len, uint64_t *el, char *suffix, unsigned long suffix_size) {
  //如果没从请求或相应获取到原始的文件名
  uint8_t                  *entity_p = payload;
  int                       ret = 0;
  char                      file_raw_name[COMMON_FILE_NAME] = {0};  //url中的文件名
  char                      child_dir[COMMON_FILE_PATH] = {0};      //url中的目录
  struct http_info *http_value = &session->http_value;
  if (!session->is_boundary && http_value->http_filename[0] == '\0') {
    ret = http_get_method_file_suffix(session->uri, child_dir, file_raw_name, suffix);
    if (suffix[0] == '\0') {
      //原始url中没有suffix，使用content-type拼接一个
      for (int i = 0; HTTP_CTYPE[i].c_type; i++) {
        if ((int)strlen(session->c_type) >= HTTP_METHOD[i].len) {
          if (0 == strncasecmp((const char *)session->c_type, HTTP_CTYPE[i].c_type, HTTP_METHOD[i].len)) {
            snprintf(suffix, suffix_size, "%s", session->c_type + HTTP_CTYPE[i].len);
            for (unsigned long i = 0; i < strlen(suffix); i++) {
              if (!isalpha(suffix[i])) {
                suffix[i] = '\0';
                break;
              }
            }
            break;
          }
        }
      }
    }
    if ((ret < 0 && file_raw_name[0] != '\0') || ret > 0) {
      snprintf(http_value->http_filename, sizeof(http_value->http_filename), "%s.%s", file_raw_name, suffix);
    }
  } else if (session->is_boundary) {
    //2.boundary中获取文件名
    entity_p = http_boundary_file_find_start(session, payload, *payload_len, payload_len, el);
  }
  if (is_request && http_value->http_filename[0] == '\0' && strlen(suffix) == 0) {
    memset(suffix, 0, suffix_size);
    strcpy(suffix, "get");
  }
  if (session->uri == NULL && http_value->http_filename[0] == '\0') {
    if (strlen(suffix) == 0 && !detect_file_type((char *)payload, *payload_len, suffix,suffix_size)) {
      if (dpi_is_utf8((char *)payload, *payload_len) > 0) {
        strncpy(suffix, "text", COMMON_SOME_TYPE);
      } else {
        strncpy(suffix, "bin", COMMON_SOME_TYPE);
      }
    }
    snprintf(http_value->http_filename, sizeof(http_value->http_filename), "http_unknuwn_name_file.%s", suffix);
  }
  if (http_value->http_filename[0] != '\0' && suffix[0] == '\0') {
    char *suffix_ = strchr(http_value->http_filename, '.');
    if (suffix_) {
      strcat(suffix, suffix_ + 1);
    }
  }
  return entity_p;
}

uint8_t http_extra_uri_filename(struct http_session *session) {
  if (session->uri == NULL) {
    return -1;
  }
  char uri_copy[2048] = {0};
  strcpy(uri_copy, session->uri);

  char *param_start = strpbrk(uri_copy, "?&=;");
  if (param_start)
    *param_start = '\0';
  char *last_slash = strrchr(uri_copy, '/');
  if (last_slash) {
    strcpy(session->decode_uri_filename, last_slash + 1);
  } else {
    strcpy(session->decode_uri_filename, uri_copy);
  }
  if (session->get_err) {
    strcat(session->decode_uri_filename, "_404");
  }
  return 1;
}

static int dissect_http_body(struct flow_info *flow, uint8_t is_request, const uint8_t *payload, int payload_len) {
  if (payload == NULL && payload_len == 0) {
    return 0;
  }

  if (NULL == flow && NULL == flow->app_session) {
    return 0;
  }

  struct http_session      *session = (struct http_session *)flow->app_session;
  struct http_info *http_value = &session->http_value;
  if (0 == g_config.http.http_switch_store_file) {
    return payload_len;
  }
  char suffix[COMMON_FILE_NAME] = {0};  //从http请求响应中获取的后缀 可以带 '.'
  int  offset = 0;                      //头长度

  //负载部分开始!
  uint8_t          *entity_p = (uint8_t *)payload;  //写入指针
  uint64_t          pl = payload_len;               //应该写入的长度
  uint64_t          el = 0;                         //结尾应该去除的长度
  struct http_file *http_file_value = NULL;
  //先处理原始文件名
  if (!session->file) {
    //1.从uri中获取文件名
    http_extra_uri_filename(session);
    //如果没从请求或相应获取到原始的文件名
    entity_p = http_dissect_raw_file_name(session, is_request, entity_p, &pl, &el, suffix, sizeof(suffix));
    if (session->is_boundary && (!pl || !entity_p)) {
      return payload_len;
    }
    offset += entity_p - payload;
  }

  int      ret = 0;
  uint64_t write_offset = 0;  //文件偏移长度
  switch (session->body_state) {
    case HTTP_BODY_TYPE_UNKNOWN:
    case HTTP_BODY_TYPE_CONTENT_LENGTH:
      write_offset = session->write_offset;
      if (session->form_urlencoded && session->record) {
        http_parse_form_urlencoded(http_value, payload, payload_len);
        if (http_value->imsi[0] != '\0') {
          precord_put_to_layer(session->record, "http", "imsi", string, http_value->imsi);
        }
        if (http_value->imei[0] != '\0') {
          precord_put_to_layer(session->record, "http", "imei", string, http_value->imei);
        }
      }
      break;
    case HTTP_BODY_TYPE_CHUNKED:
      {
        write_offset = session->write_offset;
        if (session->chunked_state == HTTP_CHUNKED_STATE_UNKNOWN) {
          //保证是第一包chunked进入
          ret = http_get_chunkedlen_from_body((char *)payload, payload_len, &el);
          if (ret > 0) {
            session->http_chunked_len = ret;
          } else if (ret == 0) {
            //chunk结束
            pl = 0;
            ret = find_packet_line_end((const uint8_t *)payload, (uint32_t)payload_len);
            if (ret >= 0) {
              offset += ret;
              el += 2;
            } else {
              offset += payload_len;
            }
            http_session_chunked_reset(session);
            break;
          } else {
            return payload_len;
          }
          ret = find_packet_line_end((const uint8_t *)payload, (uint32_t)payload_len);
          if (ret >= 0) {
            offset += ret + 2;
          } else {
            return payload_len;
          }
          session->chunked_state = HTTP_CHUNKED_STATE_WRITING;
        }
        if (session->chunked_state == HTTP_CHUNKED_STATE_WRITING) {
          entity_p += offset;
          if (session->http_chunked_write_len + payload_len >= session->http_chunked_len) {
            //这一帧中存在第二个chunk
            pl = session->http_chunked_len - session->http_chunked_write_len;
            http_freame_chunked_reset(session);
            if (offset + (int)pl + 2 <= payload_len && payload[offset + pl] == '\r' && payload[offset + pl] == '\n') {
              el += 2;
            }
          } else {
            pl = payload_len - (entity_p - payload);
            session->http_chunked_write_len += pl;
          }
          break;
        }
      }
    case HTTP_BODY_TYPE_RANGE:
      //做偏移range操作
      //这条流第一次做打开文件操作
      if (session->write_offset == 0 && session->range.start != 0) {
        write_offset = session->range.start;
      } else {
        write_offset = session->write_offset;
      }
      break;
  }
  session->write_offset = write_offset;
  if (pl) {
    session->file = http_file_async_write(flow, is_request, entity_p, pl);
  }
  //记录文件增长信息
  session->write_offset = write_offset + pl;

  return offset + pl + el;
}

// 已缓存的数据直接输出
static int http_miss(struct flow_info *flow,  uint8_t C2S, uint32_t miss_len) {
  int                       write_offset = 0;
  struct http_session      *session = (struct http_session *)flow->app_session;
  struct http_info *http_value = &session->http_value;
  if (!session) {
    return 0;
  }

  if (session->parsing_state != HTTP_PARSING_STATE_PARSING_MSG_BODY) {
    session->parsing_state = HTTP_PARSING_STATE_ERROR;
    return 0;
  }
  // switch (session->body_state) {
  //   case HTTP_BODY_TYPE_UNKNOWN:
  //   case HTTP_BODY_TYPE_CONTENT_LENGTH:
  //     write_offset = session->write_offset;
  //     break;
  //   case HTTP_BODY_TYPE_CHUNKED:
  //     //去掉chunk头操作
  //     break;
  //   case HTTP_BODY_TYPE_RANGE:
  //     //做偏移range操作
  //     //这条流第一次做打开文件操作
  //     if (session->write_offset == 0 && session->range.start != 0) {
  //       session->write_offset = session->range.start;
  //     } else {
  //       write_offset = session->write_offset;
  //     }
  //     break;
  // }

  //定位到上次写的位置
  // int      rc = fseek(http_value->fp, write_offset, SEEK_SET);
  // uint64_t wl = 0;
  // char     p[] = " ";
  // for (uint32_t i = 0; i < miss_len; i++) {
  //   wl += fwrite(p, 1, 1, http_value->fp);
  // }
  // fflush(http_value->fp);
  // file_rsm_push(http_value->file_rsm, write_offset, wl);  //记录文件增长信息
  // if (session->file) {
  //   // printf("MISS! [%s] [%ld] + [%d]\n", session->file->local, session->write_offset, miss_len);
  // // }

  session->write_offset += miss_len;
  return 0;
}

static int dissect_http_rsm(struct flow_info *flow, uint8_t direction, const uint8_t *payload, uint32_t  payload_len){
  //基于状态机模式
  //只缓存消息头，当消息头完整时解析消息头，然后切换为消息体实时输出模式
  char     t = 0;
  int64_t  hl = 0;
  int32_t  offset = 0;
  int32_t  pl = payload_len;
  uint8_t *p;

  struct http_session *s = NULL;
  struct http_cache   *c = NULL;

  if (NULL == flow->app_session) {
    flow->app_session = dpi_malloc(sizeof(struct http_session));
    if (NULL == flow->app_session) {
      goto HTTP_NEED_MORE_PKT;
    }
    memset(flow->app_session, 0, sizeof(struct http_session));
    s = (struct http_session *)flow->app_session;
    s->parsing_state = HTTP_PARSING_STATE_NEW_MSG;
  }
  s = (struct http_session *)flow->app_session;
  p = (uint8_t *)payload;
  c = s->cache;

  if (c->cache) {
    p = (uint8_t *)c->cache;
    pl = c->cache_hold;
  }

  uint8_t is_request = 0;
  if (direction == FLOW_DIR_SRC2DST) {
    is_request = 1;
    if (s->req_len + payload_len < sizeof(s->req_body)) {
      memcpy(s->req_body + s->req_len, payload, payload_len);
    }
    s->req_len += payload_len;
  } else {
    is_request = 0;
    if (s->rsp_len + payload_len < sizeof(s->rsp_body)) {
      memcpy(s->rsp_body + s->rsp_len, payload, payload_len);
    }
    s->rsp_len += payload_len;
  }

  while (pl) {
    if (s->parsing_state == HTTP_PARSING_STATE_PARSING_MSG_BODY && is_http((const char *)p, pl)) {
      s->parsing_state = HTTP_PARSING_STATE_PARSING_MSG_HEADER;
      memset(&s->http_value, 0, sizeof(s->http_value));
      s->body_state = HTTP_BODY_TYPE_UNKNOWN;
    }
    switch (s->parsing_state) {
      case HTTP_PARSING_STATE_ERROR:
        {
          if (c->cache) {
            free(c->cache);
            c->cache_hold = 0;
            c->cache_size = 0;
            c->cache = NULL;
          }
          if (s->uri != NULL) {
            free(s->uri);
            s->uri = NULL;
          }
          s->parsing_state = HTTP_PARSING_STATE_NEW_MSG;
          goto HTTP_DROP;
        }
        break;
      case HTTP_PARSING_STATE_NEW_MSG:
        {
          //初始化session动作
          if (c->cache) {
            free(c->cache);
            c->cache_hold = 0;
            c->cache_size = 0;
            c->cache = NULL;
          }
          if (is_request) {
            if (s->uri != NULL) {
              // printf("\nHTTP STATE_NEW_MSG free url [session : %p]\n", s);
              free(s->uri);
              s->uri = NULL;
            }
          }
          s->deal_content_len = 0;
          s->content_len = 0;
          memset(&s->http_value, 0, sizeof(s->http_value));
          //解决http pipeline问题
          if (is_http((const char *)p, pl)) {
            s->body_state = HTTP_BODY_TYPE_UNKNOWN;
            s->parsing_state = HTTP_PARSING_STATE_PARSING_MSG_HEADER;  // 状态迁移
          } else {
            goto HTTP_DROP;
          }
        }
        break;
      case HTTP_PARSING_STATE_PARSING_MSG_HEADER:
        {
          offset = dissect_http_head(flow, &is_request, p, pl);
          if (!offset) {
            goto HTTP_NEED_MORE_PKT;
          }
          if (offset + 2 <= pl) {
            s->parsing_state = HTTP_PARSING_STATE_PARSING_MSG_BODY;  // 状态迁移
            pl -= (offset + 2);
            p += (offset + 2);
            if (c->cache) {
              c->cache_hold -= (offset + 2);
            }
          } else {
            goto HTTP_DROP;
          }
        }
        break;
      case HTTP_PARSING_STATE_PARSING_MSG_BODY:
        {
          //先处理cache
          offset = dissect_http_body(flow, is_request, p, pl);
          s->deal_content_len += offset;
          if (s->body_state == HTTP_BODY_TYPE_CONTENT_LENGTH && s->deal_content_len == s->content_len) {
            s->parsing_state = HTTP_PARSING_STATE_NEW_MSG;  // 状态迁移
            s->req_len = 0;
            s->rsp_len = 0;
          }
          pl -= offset;
          p += offset;
          if (c->cache) {
            c->cache_hold -= offset;
          }
          if (c->cache && c->cache_hold == 0) {
            //处理完了cache中的所有数据
            free(c->cache);
            c->cache_hold = 0;
            c->cache_size = 0;
            c->cache = NULL;
            //开始处理本次的payload
            p = (uint8_t *)payload;
            pl = payload_len;
          }
        }
        break;
    }
  }

HTTP_DROP:
  if (NULL != c->cache) {
    free(c->cache);
    c->cache = NULL;
    c->cache_hold = 0;
  }
  return 0;
// HTTP 解析,需要更多报文
HTTP_NEED_MORE_PKT:
  //缓存报文
  if (NULL == c->cache) {
    c->cache_size = 1024 * 1000;  // 200K
    c->cache_hold = 0;
    c->cache = dpi_malloc(c->cache_size);
  }

  if ((int)payload_len >= (c->cache_size - c->cache_hold)) {
    // 缓存撑爆前重新申请内存
    // 创建临时缓冲区
    char *new_cache = (char *)realloc(c->cache, c->cache_size + pl + 1000);
    if (NULL == new_cache) {
      return 0;
    }
    c->cache = new_cache;
    c->cache_size += pl + 1000;
  }
  // 正常 拼装
  memcpy(c->cache + c->cache_hold, payload, payload_len);
  c->cache_hold += payload_len;
  return 0;
}

static void flow_http_finish(struct flow_info *flow)
{
    ATOMIC_FETCH_ADD(&http_free_flow);

    if (flow->app_session) {
    struct http_session *session = (struct http_session *)flow->app_session;
    struct http_cache   *c = NULL;
    c = session->cache;
    if (c->cache) {
      free(c->cache);
      c->cache_hold = 0;
      c->cache_size = 0;
      c->cache = NULL;
    }
    if (session->uri) {
      free(session->uri);
      // printf("\nHTTP FLOW FINISH free url[session : %p]\n", session);
      session->uri = NULL;
    }

    if (session->record) {
      //没有实体文件的http在这里输出
      write_http_record_log(flow,session->record);
      session->record = NULL;
    }

    free(session);
    flow->app_session = NULL;

  }
  return ;
}

int conv_http_dissect(char *key, uint8_t *this_value, uint8_t *map_value) {
  if (!this_value || !map_value) {
    return 1;
  }
  struct http_file *this = (struct http_file *)this_value;
  struct http_file *find = (struct http_file *)map_value;
  if (!find->fp) {
    if (find->local[0] == '\0') {
      find->fp = http_open_file(find);
    } else {
      find->fp = fopen(find->local, "a+");
    }
    memcpy(find->head, this->frame_body,
        (unsigned long)this->frame_len < sizeof(find->head) ? (unsigned long)this->frame_len : sizeof(find->head) - 1);
  }
  int      rc = 0;
  uint64_t wl = 0;
  // printf("[%s] [%ld] + [%d]\n", find->local, this->write_offset, this->frame_len);
  if (find->fp && this->frame_body) {
    rc = fseek(find->fp, this->write_offset, SEEK_SET);
    wl = fwrite(this->frame_body, 1, this->frame_len, find->fp);
    fflush(find->fp);
  }
  if (this->frame_body != NULL) {
    free(this->frame_body);
    this->frame_body = NULL;
  }
  if (find->frame_body != NULL) {
    free(find->frame_body);
    find->frame_body = NULL;
  }
  if (this_value != map_value) {
    dpi_flow_free(this->flow, dpi_flow_timeout_free);
    free(this);
  }
  return 0;
}

// conv_key 在框架中自动管理，不需要手动删除！
int conv_http_finish(char *key, uint8_t *value) {
  if (!value) {
    return 0;
  }
  struct http_file *file = (struct http_file *)value;
  if (file->fp) {
    http_file_close(file, 1);
  }
  if (file->frame_body != NULL) {
    free(file->frame_body);
  }
  http_write_file_log(file);


  free(value);
  return 0;
}

static int identify_http(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)
{
    //return 0; //MARK
    if (g_config.protocol_switch[PROTOCOL_HTTP] == 0)
        return PROTOCOL_UNKNOWN;

    if (payload_len < 9)
    {
        return 0;
    }

    // is response ?
    if (strncasecmp((const char *)payload, "HTTP/1.", 7) == 0)
    {
        flow->err_pcap_dump = g_config.http.error_pcap_dump; // 设定ERROR 转储 默认的开关
        flow->real_protocol_id = PROTOCOL_HTTP;
        return PROTOCOL_HTTP;
    }

    // is request ?
    for(int i = 0; HTTP_METHOD[i].method; i++)
    {
        if(0 == strncasecmp((const char*)payload, HTTP_METHOD[i].method, HTTP_METHOD[i].len))
        {
            flow->err_pcap_dump = g_config.http.error_pcap_dump; // 设定ERROR 转储 默认的开关
            flow->real_protocol_id = PROTOCOL_HTTP;
            return PROTOCOL_HTTP;
        }
    }
    return PROTOCOL_UNKNOWN;
}

extern struct decode_t decode_http;
static int http_initial(struct decode_t *decode)
{
    decode_on_port_tcp(80,   &decode_http);
    decode_on_port_tcp(8080, &decode_http);
    decode_on_port_tcp(8000, &decode_http);
    map_fields_info_register(http_field_array, PROTOCOL_HTTP, EM_HTTP_MAX, "http");
    dpi_register_proto_schema(http_field_array,EM_HTTP_MAX,"http");
    register_tbl_array(TBL_LOG_HTTP, 1, "http", NULL);

    thread_conv_array[PROTOCOL_HTTP].proto = PROTOCOL_HTTP;
    thread_conv_array[PROTOCOL_HTTP].conv_dissect = conv_http_dissect;
    thread_conv_array[PROTOCOL_HTTP].conv_timeout = conv_http_finish;

    pschema_t * schema = dpi_pschema_get_proto("http");
    high_app_proto_http_init();
    return 0;
}

static int http_destroy(struct decode_t *decode)
{
    return 0;
}

struct decode_t decode_http = {
    .name           =   "http",
#ifdef DPI_SDT_ZDY
    .identify_type  =   DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial =   http_initial,
    .pkt_identify   =   identify_http,
    .pkt_dissect    =   dissect_http_rsm,
    .pkt_miss       =   http_miss,
    .flow_finish    =   flow_http_finish,
    .decode_destroy =   http_destroy,
};


