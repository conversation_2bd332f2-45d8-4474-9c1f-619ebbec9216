-- dcerpc.lua
local mapping = {
  from_proto_name = "dcerpc",
  to_proto_name = "dcerpc",
  rule_proto_name = "dcerpc",
  common_flag = true,
  field = {
    {from_name = "version"       ,to_name = "dcerpc_version"           ,rule_name = "version",tll= 1},
    {from_name = "packet_types"       ,to_name = "dcerpc_packet_types"      ,rule_name = "packet_types",tll= 1},
    {from_name = "auth_type"       ,to_name = "dcerpc_auth_type"         ,rule_name = "auth_type",tll= 1},
    {from_name = "second_address"       ,to_name = "decrpc_second_address"    ,rule_name = "second_address",tll= 1},
    {from_name = "auth_level"       ,to_name = "decrpc_auth_level"        ,rule_name = "auth_level",tll= 1},
    {from_name = "object"       ,to_name = "decrpc_object"            ,rule_name = "object",tll= 1},
    {from_name = "interface"       ,to_name = "decrpc_interface"         ,rule_name = "interface",tll= 1},
    {from_name = "operation_number"       ,to_name = "decrpc_operation_number"  ,rule_name = "operation_number",tll= 1},
    {from_name = "endpoint"       ,to_name = "decrpc_endpoint"          ,rule_name = "endpoint",tll= 1},
  }
}
yalua_register_proto(mapping)




